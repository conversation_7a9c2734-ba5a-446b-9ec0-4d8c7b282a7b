# 测试方案文档

## 1. 测试策略概述

### 1.1 测试目标
- 确保系统功能正确性和完整性
- 验证系统性能和稳定性
- 保证用户体验和安全性
- 验证系统在各种环境下的兼容性

### 1.2 测试层次
```
┌─────────────────────────────────────┐
│           E2E测试                    │  ← 用户场景验证
├─────────────────────────────────────┤
│           集成测试                   │  ← 模块间交互验证
├─────────────────────────────────────┤
│           单元测试                   │  ← 代码逻辑验证
└─────────────────────────────────────┘
```

### 1.3 测试类型
- **功能测试**: 验证业务功能正确性
- **性能测试**: 验证系统性能指标
- **安全测试**: 验证系统安全性
- **兼容性测试**: 验证多平台兼容性
- **用户体验测试**: 验证用户交互体验

## 2. 单元测试

### 2.1 前端单元测试

#### 测试框架配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
})
```

#### 组件测试示例
```typescript
// tests/components/PuzzleBoard.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import PuzzleBoard from '@/components/PuzzleBoard/index.vue'

describe('PuzzleBoard', () => {
  const mockPuzzle = {
    id: 1,
    gridRows: 3,
    gridCols: 3,
    pieces: [
      { pieceNo: 1, imageUrl: 'piece1.jpg', positionX: 0, positionY: 0 }
    ]
  }

  it('renders puzzle board correctly', () => {
    const wrapper = mount(PuzzleBoard, {
      props: {
        puzzle: mockPuzzle,
        unlockedPieces: [1]
      }
    })

    expect(wrapper.find('.puzzle-board').exists()).toBe(true)
    expect(wrapper.findAll('.puzzle-slot')).toHaveLength(9)
  })

  it('shows unlocked pieces', () => {
    const wrapper = mount(PuzzleBoard, {
      props: {
        puzzle: mockPuzzle,
        unlockedPieces: [1]
      }
    })

    const unlockedPiece = wrapper.find('[data-piece="1"]')
    expect(unlockedPiece.exists()).toBe(true)
    expect(unlockedPiece.classes()).toContain('unlocked')
  })

  it('emits piece-click event', async () => {
    const wrapper = mount(PuzzleBoard, {
      props: {
        puzzle: mockPuzzle,
        unlockedPieces: [1]
      }
    })

    await wrapper.find('[data-piece="1"]').trigger('click')
    expect(wrapper.emitted('piece-click')).toBeTruthy()
    expect(wrapper.emitted('piece-click')[0]).toEqual([1])
  })
})
```

#### 状态管理测试
```typescript
// tests/stores/puzzle.test.ts
import { setActivePinia, createPinia } from 'pinia'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { usePuzzleStore } from '@/stores/puzzle'

describe('Puzzle Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('initializes with default state', () => {
    const store = usePuzzleStore()
    
    expect(store.currentPuzzle).toBeNull()
    expect(store.unlockedPieces).toEqual([])
    expect(store.isCompleted).toBe(false)
  })

  it('unlocks piece correctly', () => {
    const store = usePuzzleStore()
    store.currentPuzzle = { id: 1, pieceCount: 9 }
    
    store.unlockPiece(1)
    
    expect(store.unlockedPieces).toContain(1)
    expect(store.progress).toBe(1/9)
  })

  it('marks puzzle as completed when all pieces unlocked', () => {
    const store = usePuzzleStore()
    store.currentPuzzle = { id: 1, pieceCount: 2 }
    
    store.unlockPiece(1)
    store.unlockPiece(2)
    
    expect(store.isCompleted).toBe(true)
  })
})
```

### 2.2 后端单元测试

#### 测试框架配置
```typescript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/app.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}
```

#### 服务层测试
```typescript
// tests/services/puzzle.service.test.ts
import { PuzzleService } from '@/services/puzzle.service'
import { PrismaClient } from '@prisma/client'
import { CacheService } from '@/services/cache.service'

describe('PuzzleService', () => {
  let puzzleService: PuzzleService
  let mockPrisma: jest.Mocked<PrismaClient>
  let mockCache: jest.Mocked<CacheService>

  beforeEach(() => {
    mockPrisma = {
      puzzle: {
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn()
      }
    } as any

    mockCache = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn()
    } as any

    puzzleService = new PuzzleService(mockPrisma, mockCache)
  })

  describe('getPuzzleById', () => {
    it('returns puzzle from cache if available', async () => {
      const mockPuzzle = { id: 1, name: 'Test Puzzle' }
      mockCache.get.mockResolvedValue(mockPuzzle)

      const result = await puzzleService.getPuzzleById(1)

      expect(result).toEqual(mockPuzzle)
      expect(mockCache.get).toHaveBeenCalledWith('puzzle:1:info')
      expect(mockPrisma.puzzle.findUnique).not.toHaveBeenCalled()
    })

    it('fetches from database and caches if not in cache', async () => {
      const mockPuzzle = { id: 1, name: 'Test Puzzle' }
      mockCache.get.mockResolvedValue(null)
      mockPrisma.puzzle.findUnique.mockResolvedValue(mockPuzzle)

      const result = await puzzleService.getPuzzleById(1)

      expect(result).toEqual(mockPuzzle)
      expect(mockPrisma.puzzle.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { pieces: true }
      })
      expect(mockCache.set).toHaveBeenCalledWith('puzzle:1:info', mockPuzzle, 3600)
    })
  })

  describe('updateUserProgress', () => {
    it('unlocks new piece and updates progress', async () => {
      const mockState = {
        userId: 'user1',
        puzzleId: 1,
        unlockedPieces: [1, 2],
        unlockCount: 2,
        isCompleted: false
      }

      const mockPuzzle = { id: 1, pieceCount: 4 }
      
      jest.spyOn(puzzleService, 'getUserPuzzleState').mockResolvedValue(mockState)
      jest.spyOn(puzzleService, 'getPuzzleById').mockResolvedValue(mockPuzzle)
      
      mockPrisma.userPuzzleState.upsert.mockResolvedValue({
        ...mockState,
        unlockedPieces: [1, 2, 3],
        unlockCount: 3
      })

      const result = await puzzleService.updateUserProgress('user1', 1, 3)

      expect(result.unlockedPieces).toContain(3)
      expect(result.unlockCount).toBe(3)
    })
  })
})
```

## 3. 集成测试

### 3.1 API集成测试
```typescript
// tests/integration/api.test.ts
import request from 'supertest'
import { app } from '@/app'
import { setupTestDatabase, cleanupTestDatabase } from './helpers/database'

describe('API Integration Tests', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
  })

  describe('GET /api/v1/puzzles/:id', () => {
    it('returns puzzle information', async () => {
      const response = await request(app)
        .get('/api/v1/puzzles/1')
        .expect(200)

      expect(response.body).toMatchObject({
        code: 200,
        data: {
          id: 1,
          name: expect.any(String),
          pieceCount: expect.any(Number),
          pieces: expect.any(Array)
        }
      })
    })

    it('returns 404 for non-existent puzzle', async () => {
      const response = await request(app)
        .get('/api/v1/puzzles/999')
        .expect(404)

      expect(response.body.code).toBe(404)
    })
  })

  describe('POST /api/v1/qrcodes/verify', () => {
    it('verifies valid QR code and unlocks piece', async () => {
      const qrToken = 'valid-token-123'
      const signature = 'valid-signature'
      const timestamp = Date.now()

      const response = await request(app)
        .post('/api/v1/qrcodes/verify')
        .set('X-User-ID', 'test-user')
        .send({ qrToken, signature, timestamp })
        .expect(200)

      expect(response.body.data).toMatchObject({
        puzzleId: expect.any(Number),
        pieceNo: expect.any(Number),
        isNewUnlock: true
      })
    })

    it('rejects invalid signature', async () => {
      const response = await request(app)
        .post('/api/v1/qrcodes/verify')
        .set('X-User-ID', 'test-user')
        .send({
          qrToken: 'valid-token',
          signature: 'invalid-signature',
          timestamp: Date.now()
        })
        .expect(400)

      expect(response.body.code).toBe(10002)
    })
  })
})
```

### 3.2 数据库集成测试
```typescript
// tests/integration/database.test.ts
import { PrismaClient } from '@prisma/client'

describe('Database Integration', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL
        }
      }
    })
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('creates and retrieves puzzle correctly', async () => {
    const puzzle = await prisma.puzzle.create({
      data: {
        name: 'Test Puzzle',
        originalImageUrl: 'test.jpg',
        pieceCount: 9,
        gridRows: 3,
        gridCols: 3,
        status: 'active'
      }
    })

    expect(puzzle.id).toBeDefined()
    expect(puzzle.name).toBe('Test Puzzle')

    const retrieved = await prisma.puzzle.findUnique({
      where: { id: puzzle.id }
    })

    expect(retrieved).toEqual(puzzle)
  })

  it('maintains referential integrity', async () => {
    const puzzle = await prisma.puzzle.create({
      data: {
        name: 'Test Puzzle 2',
        originalImageUrl: 'test2.jpg',
        pieceCount: 4,
        gridRows: 2,
        gridCols: 2,
        status: 'active'
      }
    })

    await prisma.puzzlePiece.create({
      data: {
        puzzleId: puzzle.id,
        pieceNo: 1,
        imageUrl: 'piece1.jpg',
        positionX: 0,
        positionY: 0,
        width: 100,
        height: 100
      }
    })

    // 删除拼图应该级联删除碎片
    await prisma.puzzle.delete({
      where: { id: puzzle.id }
    })

    const pieces = await prisma.puzzlePiece.findMany({
      where: { puzzleId: puzzle.id }
    })

    expect(pieces).toHaveLength(0)
  })
})
```

## 4. 端到端测试

### 4.1 Playwright E2E测试
```typescript
// tests/e2e/puzzle-game.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Puzzle Game E2E', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('complete puzzle flow', async ({ page }) => {
    // 1. 进入拼图页面
    await page.click('[data-testid="start-puzzle"]')
    await expect(page).toHaveURL('/puzzle/1')

    // 2. 检查初始状态
    await expect(page.locator('.puzzle-board')).toBeVisible()
    await expect(page.locator('.empty-slot')).toHaveCount(9)

    // 3. 模拟扫码解锁碎片
    await page.click('[data-testid="scan-qr"]')
    
    // 模拟微信扫码返回结果
    await page.evaluate(() => {
      window.wx.scanQRCode({
        success: (res) => {
          res.resultStr = 'https://example.com/scan?token=test123&sig=abc&ts=123456'
        }
      })
    })

    // 4. 验证碎片解锁
    await expect(page.locator('[data-piece="1"]')).toBeVisible()
    await expect(page.locator('.progress-text')).toContainText('1/9')

    // 5. 继续解锁其他碎片（模拟）
    for (let i = 2; i <= 9; i++) {
      await page.evaluate((pieceNo) => {
        // 模拟解锁碎片
        window.unlockPiece(pieceNo)
      }, i)
    }

    // 6. 验证拼图完成
    await expect(page.locator('.prize-modal')).toBeVisible()
    await expect(page.locator('.redeem-code')).toBeVisible()
    
    // 7. 测试复制兑换码功能
    await page.click('[data-testid="copy-code"]')
    await expect(page.locator('.toast')).toContainText('复制成功')
  })

  test('handles scan errors gracefully', async ({ page }) => {
    await page.click('[data-testid="start-puzzle"]')
    await page.click('[data-testid="scan-qr"]')

    // 模拟扫码失败
    await page.evaluate(() => {
      window.wx.scanQRCode({
        fail: () => {
          throw new Error('扫码失败')
        }
      })
    })

    await expect(page.locator('.error-message')).toContainText('扫描失败')
  })

  test('prevents duplicate piece unlock', async ({ page }) => {
    await page.click('[data-testid="start-puzzle"]')
    
    // 解锁同一个碎片两次
    await page.evaluate(() => {
      window.unlockPiece(1)
      window.unlockPiece(1)
    })

    // 验证只解锁一次
    await expect(page.locator('.progress-text')).toContainText('1/9')
  })
})
```

### 4.2 移动端测试
```typescript
// tests/e2e/mobile.spec.ts
import { test, expect, devices } from '@playwright/test'

test.use({ ...devices['iPhone 12'] })

test.describe('Mobile Experience', () => {
  test('responsive design works correctly', async ({ page }) => {
    await page.goto('/')
    
    // 检查移动端布局
    await expect(page.locator('.puzzle-board')).toHaveCSS('display', 'grid')
    await expect(page.locator('.scan-button')).toBeVisible()
    
    // 检查触摸交互
    await page.tap('[data-testid="scan-qr"]')
    await expect(page.locator('.scan-modal')).toBeVisible()
  })

  test('handles touch gestures', async ({ page }) => {
    await page.goto('/puzzle/1')
    
    // 测试拖拽手势（如果有的话）
    const piece = page.locator('[data-piece="1"]')
    await piece.hover()
    await page.mouse.down()
    await page.mouse.move(100, 100)
    await page.mouse.up()
  })
})
```

## 5. 性能测试

### 5.1 负载测试
```javascript
// tests/performance/load-test.js
import http from 'k6/http'
import { check, sleep } from 'k6'

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // 2分钟内增加到100用户
    { duration: '5m', target: 100 }, // 保持100用户5分钟
    { duration: '2m', target: 200 }, // 2分钟内增加到200用户
    { duration: '5m', target: 200 }, // 保持200用户5分钟
    { duration: '2m', target: 0 },   // 2分钟内减少到0用户
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%的请求响应时间小于500ms
    http_req_failed: ['rate<0.1'],    // 错误率小于10%
  }
}

export default function() {
  // 测试获取拼图信息
  let response = http.get('https://api.example.com/api/v1/puzzles/1')
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  })

  sleep(1)

  // 测试二维码验证
  const qrPayload = JSON.stringify({
    qrToken: 'test-token-123',
    signature: 'test-signature',
    timestamp: Date.now()
  })

  response = http.post('https://api.example.com/api/v1/qrcodes/verify', qrPayload, {
    headers: {
      'Content-Type': 'application/json',
      'X-User-ID': `user-${__VU}-${__ITER}`
    }
  })

  check(response, {
    'verify status is 200': (r) => r.status === 200,
    'verify response time < 1000ms': (r) => r.timings.duration < 1000,
  })

  sleep(2)
}
```

### 5.2 压力测试
```javascript
// tests/performance/stress-test.js
import http from 'k6/http'
import { check } from 'k6'

export let options = {
  stages: [
    { duration: '10m', target: 500 },  // 10分钟内增加到500用户
    { duration: '30m', target: 500 },  // 保持500用户30分钟
    { duration: '10m', target: 1000 }, // 10分钟内增加到1000用户
    { duration: '30m', target: 1000 }, // 保持1000用户30分钟
    { duration: '10m', target: 0 },    // 10分钟内减少到0用户
  ]
}

export default function() {
  const responses = http.batch([
    ['GET', 'https://api.example.com/api/v1/puzzles/1'],
    ['GET', 'https://api.example.com/api/v1/puzzles/2'],
    ['GET', 'https://api.example.com/api/v1/puzzles/3'],
  ])

  responses.forEach((response) => {
    check(response, {
      'status is 200': (r) => r.status === 200,
    })
  })
}
```

## 6. 安全测试

### 6.1 API安全测试
```typescript
// tests/security/api-security.test.ts
import request from 'supertest'
import { app } from '@/app'

describe('API Security Tests', () => {
  describe('Authentication', () => {
    it('rejects requests without user ID', async () => {
      await request(app)
        .post('/api/v1/qrcodes/verify')
        .send({ qrToken: 'test', signature: 'test', timestamp: Date.now() })
        .expect(401)
    })

    it('validates user ID format', async () => {
      await request(app)
        .post('/api/v1/qrcodes/verify')
        .set('X-User-ID', 'invalid-format')
        .send({ qrToken: 'test', signature: 'test', timestamp: Date.now() })
        .expect(401)
    })
  })

  describe('Input Validation', () => {
    it('rejects SQL injection attempts', async () => {
      await request(app)
        .get('/api/v1/puzzles/1; DROP TABLE puzzles;--')
        .expect(400)
    })

    it('sanitizes XSS attempts', async () => {
      const response = await request(app)
        .post('/api/v1/qrcodes/verify')
        .set('X-User-ID', 'test-user')
        .send({
          qrToken: '<script>alert("xss")</script>',
          signature: 'test',
          timestamp: Date.now()
        })
        .expect(400)

      expect(response.body.message).not.toContain('<script>')
    })
  })

  describe('Rate Limiting', () => {
    it('enforces rate limits', async () => {
      const requests = Array(15).fill(null).map(() =>
        request(app)
          .post('/api/v1/qrcodes/verify')
          .set('X-User-ID', 'test-user')
          .send({ qrToken: 'test', signature: 'test', timestamp: Date.now() })
      )

      const responses = await Promise.all(requests)
      const rateLimitedResponses = responses.filter(r => r.status === 429)
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0)
    })
  })
})
```

## 7. 测试数据管理

### 7.1 测试数据工厂
```typescript
// tests/helpers/factories.ts
import { faker } from '@faker-js/faker'

export const PuzzleFactory = {
  create: (overrides = {}) => ({
    id: faker.number.int({ min: 1, max: 1000 }),
    name: faker.lorem.words(3),
    description: faker.lorem.sentence(),
    originalImageUrl: faker.image.url(),
    pieceCount: 9,
    gridRows: 3,
    gridCols: 3,
    status: 'active',
    startTime: faker.date.past(),
    endTime: faker.date.future(),
    ...overrides
  }),

  createMany: (count: number, overrides = {}) => 
    Array(count).fill(null).map(() => PuzzleFactory.create(overrides))
}

export const UserStateFactory = {
  create: (overrides = {}) => ({
    userId: faker.string.uuid(),
    puzzleId: faker.number.int({ min: 1, max: 100 }),
    unlockedPieces: faker.helpers.arrayElements([1, 2, 3, 4, 5, 6, 7, 8, 9]),
    unlockCount: faker.number.int({ min: 0, max: 9 }),
    isCompleted: faker.datatype.boolean(),
    ...overrides
  })
}
```

### 7.2 测试环境清理
```typescript
// tests/helpers/cleanup.ts
export const cleanupTestData = async () => {
  await prisma.operationLog.deleteMany()
  await prisma.redeemCode.deleteMany()
  await prisma.userPuzzleState.deleteMany()
  await prisma.qrcode.deleteMany()
  await prisma.puzzlePiece.deleteMany()
  await prisma.prize.deleteMany()
  await prisma.puzzle.deleteMany()
}

export const seedTestData = async () => {
  const puzzle = await prisma.puzzle.create({
    data: PuzzleFactory.create({ id: 1 })
  })

  const pieces = Array(9).fill(null).map((_, index) => ({
    puzzleId: puzzle.id,
    pieceNo: index + 1,
    imageUrl: `piece_${index + 1}.jpg`,
    positionX: (index % 3) * 100,
    positionY: Math.floor(index / 3) * 100,
    width: 100,
    height: 100
  }))

  await prisma.puzzlePiece.createMany({ data: pieces })
}
```

## 8. 测试报告和CI集成

### 8.1 测试报告生成
```json
{
  "scripts": {
    "test": "vitest",
    "test:coverage": "vitest --coverage",
    "test:e2e": "playwright test",
    "test:integration": "jest --config jest.integration.config.js",
    "test:performance": "k6 run tests/performance/load-test.js",
    "test:all": "npm run test && npm run test:integration && npm run test:e2e"
  }
}
```

### 8.2 CI/CD集成
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:coverage
      - uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test
          MYSQL_DATABASE: test_db
        options: --health-cmd="mysqladmin ping" --health-interval=10s
      redis:
        image: redis:6.2
        options: --health-cmd="redis-cli ping" --health-interval=10s
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e
```
