# API接口设计文档

## 1. 接口概述

本项目采用RESTful API设计风格，所有接口返回JSON格式数据。接口遵循统一的响应格式和错误处理机制。

## 2. 通用规范

### 2.1 请求格式
- **协议**: HTTPS
- **Content-Type**: application/json
- **字符编码**: UTF-8

### 2.2 响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200000
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "详细错误信息",
  "timestamp": 1640995200000
}
```

### 2.3 状态码定义
- **200**: 成功
- **400**: 请求参数错误
- **401**: 未授权
- **403**: 禁止访问
- **404**: 资源不存在
- **429**: 请求过于频繁
- **500**: 服务器内部错误

## 3. 核心接口设计

### 3.1 拼图相关接口

#### 3.1.1 获取拼图信息
```
GET /api/v1/puzzles/{puzzleId}
```

**请求参数**:
- `puzzleId` (path): 拼图ID

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "春节拼图",
    "description": "2024年春节主题拼图",
    "originalImageUrl": "https://cdn.example.com/puzzle/original.jpg",
    "pieceCount": 9,
    "gridRows": 3,
    "gridCols": 3,
    "status": "active",
    "startTime": "2024-01-01T00:00:00Z",
    "endTime": "2024-01-31T23:59:59Z",
    "pieces": [
      {
        "pieceNo": 1,
        "imageUrl": "https://cdn.example.com/puzzle/piece_1.jpg",
        "positionX": 0,
        "positionY": 0,
        "width": 100,
        "height": 100
      }
    ]
  }
}
```

#### 3.1.2 获取用户拼图状态
```
GET /api/v1/puzzles/{puzzleId}/user-state
```

**请求头**:
- `X-User-ID`: 用户标识(openid或设备ID)

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "puzzleId": 1,
    "userId": "user123",
    "unlockedPieces": [1, 3, 5],
    "unlockCount": 3,
    "isCompleted": false,
    "completedAt": null,
    "firstUnlockAt": "2024-01-01T10:00:00Z",
    "lastUnlockAt": "2024-01-01T15:30:00Z"
  }
}
```

### 3.2 二维码相关接口

#### 3.2.1 验证二维码
```
POST /api/v1/qrcodes/verify
```

**请求参数**:
```json
{
  "qrToken": "abc123def456",
  "signature": "签名字符串",
  "timestamp": 1640995200000
}
```

**请求头**:
- `X-User-ID`: 用户标识

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "puzzleId": 1,
    "pieceNo": 3,
    "pieceInfo": {
      "imageUrl": "https://cdn.example.com/puzzle/piece_3.jpg",
      "positionX": 200,
      "positionY": 0,
      "width": 100,
      "height": 100
    },
    "isNewUnlock": true,
    "currentProgress": {
      "unlockedCount": 4,
      "totalCount": 9,
      "isCompleted": false
    }
  }
}
```

#### 3.2.2 生成二维码
```
POST /api/v1/qrcodes/generate
```

**请求参数**:
```json
{
  "puzzleId": 1,
  "pieceNo": 3,
  "expireHours": 24
}
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "qrToken": "abc123def456",
    "qrContent": "https://example.com/scan?token=abc123def456&sig=xxx",
    "qrImageUrl": "https://cdn.example.com/qr/abc123def456.png",
    "expireAt": "2024-01-02T10:00:00Z"
  }
}
```

### 3.3 奖品相关接口

#### 3.3.1 生成兑换码
```
POST /api/v1/prizes/generate-code
```

**请求参数**:
```json
{
  "puzzleId": 1
}
```

**请求头**:
- `X-User-ID`: 用户标识

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "redeemCode": "PRIZE2024ABC123",
    "prizeName": "新年礼品",
    "prizeDescription": "精美新年礼品一份",
    "redeemAddress": "北京市朝阳区xxx商场一楼服务台",
    "expireAt": "2024-01-03T10:00:00Z",
    "generatedAt": "2024-01-01T10:00:00Z"
  }
}
```

#### 3.3.2 查询兑换码状态
```
GET /api/v1/prizes/redeem-codes/{code}
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "redeemCode": "PRIZE2024ABC123",
    "status": "active",
    "prizeName": "新年礼品",
    "redeemAddress": "北京市朝阳区xxx商场一楼服务台",
    "expireAt": "2024-01-03T10:00:00Z",
    "isExpired": false
  }
}
```

### 3.4 统计相关接口

#### 3.4.1 获取拼图统计
```
GET /api/v1/puzzles/{puzzleId}/stats
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "totalScans": 1250,
    "uniqueUsers": 856,
    "completedUsers": 123,
    "completionRate": 0.144,
    "pieceUnlockStats": [
      {"pieceNo": 1, "unlockCount": 800},
      {"pieceNo": 2, "unlockCount": 750}
    ],
    "dailyStats": [
      {"date": "2024-01-01", "scans": 200, "completions": 15}
    ]
  }
}
```

## 4. 错误码定义

### 4.1 业务错误码
- **10001**: 拼图不存在或已过期
- **10002**: 二维码无效或已过期
- **10003**: 二维码已被使用
- **10004**: 用户已完成该拼图
- **10005**: 奖品库存不足
- **10006**: 兑换码已过期
- **10007**: 兑换码已使用
- **10008**: 扫码过于频繁

### 4.2 系统错误码
- **20001**: 数据库连接失败
- **20002**: 缓存服务异常
- **20003**: 文件上传失败
- **20004**: 第三方服务调用失败

## 5. 接口安全

### 5.1 签名验证
二维码验证接口需要进行签名验证：

```javascript
// 签名算法
const signature = md5(qrToken + timestamp + secretKey)
```

### 5.2 频率限制
- **扫码接口**: 每用户每分钟最多10次
- **生成兑换码**: 每用户每天最多1次
- **IP限制**: 每IP每分钟最多100次请求

### 5.3 用户身份验证
- 微信环境下使用openid作为用户标识
- 非微信环境使用设备指纹或UUID
- 关键操作需要验证用户身份

## 6. 接口版本管理

### 6.1 版本策略
- 使用URL路径版本控制: `/api/v1/`
- 向后兼容原则，新版本不破坏旧版本
- 废弃接口提前通知，保留过渡期

### 6.2 版本变更记录
- **v1.0**: 初始版本，包含基础功能
- **v1.1**: 增加统计接口和批量操作
- **v1.2**: 优化性能，增加缓存机制

## 7. 接口测试

### 7.1 测试环境
- **开发环境**: https://dev-api.example.com
- **测试环境**: https://test-api.example.com
- **生产环境**: https://api.example.com

### 7.2 测试用例
每个接口需要包含以下测试用例：
- 正常流程测试
- 参数校验测试
- 边界条件测试
- 异常情况测试
- 性能压力测试

## 8. 接口文档

### 8.1 文档工具
使用Swagger/OpenAPI生成接口文档，支持：
- 在线调试功能
- 自动生成SDK
- 接口变更通知

### 8.2 文档维护
- 代码注释自动生成文档
- 接口变更及时更新文档
- 提供示例代码和最佳实践
