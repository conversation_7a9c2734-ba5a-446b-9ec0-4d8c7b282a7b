【AI 视角复述】

1. 业务目标  
   把一张完整的主图切成 N 份碎片，每份碎片对应一个唯一二维码。移动端 H5 作为游戏入口，用户通过“扫二维码”把对应碎片解锁；当 N 份全部解锁后，H5 将碎片自动拼回主图，并弹出“拼图完成”页面，在页面里展示一个带有效期的奖品兑换码和领奖地址。

2. 核心交互链  
   
   - 首次进入：H5 展示空拼图底板 + 已解锁/未解锁状态。  
   - 扫任意二维码 → 后端校验二维码 → 返回对应碎片编号 → 前端把该碎片渲染到正确位置。  
   - 解锁计数器累加；当计数器 = N 时触发“拼图完成”事件。  
   - 完成后：  
     – 前端显示合成后的完整图；  
     – 调用后端 /generate-code 接口获取一次性奖品兑换码（含过期时间）；  
     – 展示兑换码 + 领奖地址 + “复制”按钮。  

3. 技术细节  
   
   - 切图逻辑：  
     – 预设主图在服务器切成 N 张固定尺寸的小图（或运行时 CSS clip）。  
   - 二维码映射：  
     – 数据库表 qrcodes(id, piece_no, used, expire_at)。  
   - 状态管理：  
     – 用户维度：localStorage / 微信 openid 存储已解锁 piece_no 列表；  
     – 全局维度：Redis 计数器防刷，确保同一张二维码只能被首次扫描者解锁。  
   - 防作弊：  
     – 二维码只接受 GET + token 签名校验；  
     – 完成拼图后兑换码与用户身份绑定，防止截屏二次使用。  

4. 端能力  
   
   - 纯 H5，可嵌入微信公众号、小程序 web-view 或浏览器。  
   - 扫码依赖微信 JS-SDK 或浏览器原生扫一扫。  

5. 可扩展  
   
   - 碎片可带动画、音效；  
   - 支持不同难度（4、9、16 片）；  
   - 后台实时查看解锁热力图，运营可动态加奖品库存。
