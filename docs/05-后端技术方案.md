# 后端技术方案文档

## 1. 技术栈选择

### 1.1 运行环境
- **Node.js 18+**: 长期支持版本，性能稳定
- **TypeScript**: 提供类型安全和更好的开发体验

### 1.2 Web框架
- **Express.js**: 成熟稳定的Node.js Web框架
- **Helmet**: 安全中间件，设置各种HTTP头
- **CORS**: 跨域资源共享配置
- **Compression**: 响应压缩中间件

### 1.3 数据库相关
- **MySQL 8.0**: 主数据库，支持JSON字段
- **Prisma**: 现代化ORM，类型安全的数据库访问
- **Redis 6.0**: 缓存和会话存储
- **ioredis**: Redis客户端库

### 1.4 工具库
- **Joi**: 数据验证库
- **jsonwebtoken**: JWT令牌处理
- **bcrypt**: 密码加密
- **sharp**: 图片处理
- **qrcode**: 二维码生成
- **dayjs**: 日期处理

## 2. 项目结构

```
src/
├── controllers/           # 控制器层
│   ├── puzzle.controller.ts
│   ├── qrcode.controller.ts
│   ├── prize.controller.ts
│   └── stats.controller.ts
├── services/             # 业务逻辑层
│   ├── puzzle.service.ts
│   ├── qrcode.service.ts
│   ├── prize.service.ts
│   ├── user.service.ts
│   └── cache.service.ts
├── repositories/         # 数据访问层
│   ├── puzzle.repository.ts
│   ├── qrcode.repository.ts
│   ├── prize.repository.ts
│   └── user.repository.ts
├── middlewares/          # 中间件
│   ├── auth.middleware.ts
│   ├── validation.middleware.ts
│   ├── rateLimit.middleware.ts
│   └── error.middleware.ts
├── utils/               # 工具函数
│   ├── crypto.ts        # 加密工具
│   ├── image.ts         # 图片处理
│   ├── logger.ts        # 日志工具
│   └── response.ts      # 响应格式化
├── types/               # 类型定义
│   ├── puzzle.types.ts
│   ├── user.types.ts
│   └── api.types.ts
├── config/              # 配置文件
│   ├── database.ts
│   ├── redis.ts
│   ├── app.ts
│   └── constants.ts
├── routes/              # 路由定义
│   ├── puzzle.routes.ts
│   ├── qrcode.routes.ts
│   ├── prize.routes.ts
│   └── index.ts
└── app.ts               # 应用入口
```

## 3. 核心服务设计

### 3.1 拼图服务 (PuzzleService)

```typescript
// services/puzzle.service.ts
import { PrismaClient } from '@prisma/client'
import { CacheService } from './cache.service'

export class PuzzleService {
  constructor(
    private prisma: PrismaClient,
    private cache: CacheService
  ) {}

  async getPuzzleById(id: number): Promise<PuzzleInfo | null> {
    // 先从缓存获取
    const cacheKey = `puzzle:${id}:info`
    let puzzle = await this.cache.get(cacheKey)
    
    if (!puzzle) {
      // 从数据库获取
      puzzle = await this.prisma.puzzle.findUnique({
        where: { id },
        include: { pieces: true }
      })
      
      if (puzzle) {
        // 缓存1小时
        await this.cache.set(cacheKey, puzzle, 3600)
      }
    }
    
    return puzzle
  }

  async getUserPuzzleState(userId: string, puzzleId: number): Promise<UserPuzzleState> {
    const cacheKey = `user:${userId}:puzzle:${puzzleId}:state`
    let state = await this.cache.get(cacheKey)
    
    if (!state) {
      state = await this.prisma.userPuzzleState.findUnique({
        where: {
          userId_puzzleId: { userId, puzzleId }
        }
      })
      
      if (state) {
        await this.cache.set(cacheKey, state, 1800) // 30分钟
      }
    }
    
    return state || this.createDefaultState(userId, puzzleId)
  }

  async updateUserProgress(
    userId: string, 
    puzzleId: number, 
    pieceNo: number
  ): Promise<UserPuzzleState> {
    const state = await this.getUserPuzzleState(userId, puzzleId)
    const unlockedPieces = state.unlockedPieces as number[]
    
    if (!unlockedPieces.includes(pieceNo)) {
      unlockedPieces.push(pieceNo)
      
      const puzzle = await this.getPuzzleById(puzzleId)
      const isCompleted = unlockedPieces.length === puzzle?.pieceCount
      
      const updatedState = await this.prisma.userPuzzleState.upsert({
        where: {
          userId_puzzleId: { userId, puzzleId }
        },
        update: {
          unlockedPieces,
          unlockCount: unlockedPieces.length,
          isCompleted,
          completedAt: isCompleted ? new Date() : null,
          lastUnlockAt: new Date()
        },
        create: {
          userId,
          puzzleId,
          unlockedPieces,
          unlockCount: unlockedPieces.length,
          isCompleted,
          completedAt: isCompleted ? new Date() : null,
          firstUnlockAt: new Date(),
          lastUnlockAt: new Date()
        }
      })
      
      // 更新缓存
      const cacheKey = `user:${userId}:puzzle:${puzzleId}:state`
      await this.cache.set(cacheKey, updatedState, 1800)
      
      return updatedState
    }
    
    return state
  }
}
```

### 3.2 二维码服务 (QRCodeService)

```typescript
// services/qrcode.service.ts
import { createHash, createHmac } from 'crypto'
import QRCode from 'qrcode'

export class QRCodeService {
  private readonly secretKey = process.env.QR_SECRET_KEY!

  async generateQRCode(puzzleId: number, pieceNo: number): Promise<QRCodeInfo> {
    const qrToken = this.generateToken()
    const timestamp = Date.now()
    const signature = this.generateSignature(qrToken, timestamp)
    
    const qrContent = `${process.env.APP_URL}/scan?token=${qrToken}&sig=${signature}&ts=${timestamp}`
    
    // 生成二维码图片
    const qrImageBuffer = await QRCode.toBuffer(qrContent, {
      width: 300,
      margin: 2
    })
    
    // 上传到OSS或保存到本地
    const qrImageUrl = await this.uploadQRImage(qrToken, qrImageBuffer)
    
    // 保存到数据库
    const expireAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
    
    await this.prisma.qrcode.create({
      data: {
        puzzleId,
        pieceNo,
        qrToken,
        qrContent,
        expireAt
      }
    })
    
    return {
      qrToken,
      qrContent,
      qrImageUrl,
      expireAt
    }
  }

  async verifyQRCode(qrToken: string, signature: string, timestamp: number): Promise<QRCodeVerifyResult> {
    // 验证时间戳（防重放攻击）
    const now = Date.now()
    if (Math.abs(now - timestamp) > 5 * 60 * 1000) { // 5分钟有效期
      throw new Error('二维码已过期')
    }
    
    // 验证签名
    const expectedSignature = this.generateSignature(qrToken, timestamp)
    if (signature !== expectedSignature) {
      throw new Error('二维码签名无效')
    }
    
    // 查询二维码信息
    const qrcode = await this.prisma.qrcode.findUnique({
      where: { qrToken },
      include: { puzzle: true }
    })
    
    if (!qrcode) {
      throw new Error('二维码不存在')
    }
    
    if (qrcode.expireAt < new Date()) {
      throw new Error('二维码已过期')
    }
    
    if (qrcode.isUsed) {
      throw new Error('二维码已被使用')
    }
    
    return {
      puzzleId: qrcode.puzzleId,
      pieceNo: qrcode.pieceNo,
      puzzle: qrcode.puzzle
    }
  }

  async markQRCodeAsUsed(qrToken: string, userId: string): Promise<void> {
    await this.prisma.qrcode.update({
      where: { qrToken },
      data: {
        isUsed: true,
        usedBy: userId,
        usedAt: new Date()
      }
    })
  }

  private generateToken(): string {
    return createHash('md5')
      .update(`${Date.now()}-${Math.random()}`)
      .digest('hex')
  }

  private generateSignature(token: string, timestamp: number): string {
    return createHmac('sha256', this.secretKey)
      .update(`${token}${timestamp}`)
      .digest('hex')
  }
}
```

### 3.3 奖品服务 (PrizeService)

```typescript
// services/prize.service.ts
export class PrizeService {
  async generateRedeemCode(userId: string, puzzleId: number): Promise<RedeemCodeInfo> {
    // 检查用户是否已完成拼图
    const userState = await this.puzzleService.getUserPuzzleState(userId, puzzleId)
    if (!userState.isCompleted) {
      throw new Error('拼图未完成，无法生成兑换码')
    }
    
    // 检查是否已生成过兑换码
    const existingCode = await this.prisma.redeemCode.findFirst({
      where: { userId, puzzleId }
    })
    
    if (existingCode) {
      return this.formatRedeemCodeInfo(existingCode)
    }
    
    // 获取奖品信息
    const prize = await this.prisma.prize.findFirst({
      where: { 
        puzzleId,
        status: 'active',
        remainingStock: { gt: 0 }
      }
    })
    
    if (!prize) {
      throw new Error('奖品库存不足')
    }
    
    // 生成兑换码
    const redeemCode = this.generateCode()
    const expireAt = new Date(Date.now() + prize.expireHours * 60 * 60 * 1000)
    
    // 使用事务确保数据一致性
    const result = await this.prisma.$transaction(async (tx) => {
      // 减少库存
      await tx.prize.update({
        where: { id: prize.id },
        data: { remainingStock: { decrement: 1 } }
      })
      
      // 创建兑换码
      return tx.redeemCode.create({
        data: {
          code: redeemCode,
          userId,
          puzzleId,
          prizeId: prize.id,
          expireAt
        },
        include: { prize: true }
      })
    })
    
    return this.formatRedeemCodeInfo(result)
  }

  private generateCode(): string {
    const prefix = 'PRIZE'
    const timestamp = Date.now().toString(36).toUpperCase()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `${prefix}${timestamp}${random}`
  }
}
```

## 4. 中间件设计

### 4.1 频率限制中间件

```typescript
// middlewares/rateLimit.middleware.ts
import rateLimit from 'express-rate-limit'
import RedisStore from 'rate-limit-redis'

export const createRateLimit = (options: {
  windowMs: number
  max: number
  keyGenerator?: (req: Request) => string
}) => {
  return rateLimit({
    store: new RedisStore({
      client: redisClient,
      prefix: 'rate_limit:'
    }),
    windowMs: options.windowMs,
    max: options.max,
    keyGenerator: options.keyGenerator || ((req) => req.ip),
    message: {
      code: 429,
      message: '请求过于频繁，请稍后再试'
    }
  })
}

// 扫码接口限制：每用户每分钟10次
export const scanRateLimit = createRateLimit({
  windowMs: 60 * 1000,
  max: 10,
  keyGenerator: (req) => req.headers['x-user-id'] as string || req.ip
})

// IP限制：每IP每分钟100次
export const ipRateLimit = createRateLimit({
  windowMs: 60 * 1000,
  max: 100
})
```

### 4.2 用户身份验证中间件

```typescript
// middlewares/auth.middleware.ts
export const authenticateUser = (req: Request, res: Response, next: NextFunction) => {
  const userId = req.headers['x-user-id'] as string
  
  if (!userId) {
    return res.status(401).json({
      code: 401,
      message: '缺少用户标识'
    })
  }
  
  // 验证用户ID格式
  if (!isValidUserId(userId)) {
    return res.status(401).json({
      code: 401,
      message: '用户标识格式无效'
    })
  }
  
  req.userId = userId
  next()
}
```

## 5. 缓存策略

### 5.1 缓存层次
- **L1缓存**: 应用内存缓存（Node.js进程内）
- **L2缓存**: Redis分布式缓存
- **L3缓存**: CDN边缘缓存

### 5.2 缓存模式
- **Cache-Aside**: 应用程序管理缓存
- **Write-Through**: 写入时同步更新缓存
- **Write-Behind**: 异步写入数据库

### 5.3 缓存失效策略
- **TTL过期**: 设置合理的过期时间
- **主动失效**: 数据更新时清除相关缓存
- **版本控制**: 使用版本号管理缓存一致性

## 6. 错误处理

### 6.1 全局错误处理中间件

```typescript
// middlewares/error.middleware.ts
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('Unhandled error:', error)
  
  if (error instanceof ValidationError) {
    return res.status(400).json({
      code: 400,
      message: '参数验证失败',
      error: error.message
    })
  }
  
  if (error instanceof BusinessError) {
    return res.status(400).json({
      code: error.code,
      message: error.message
    })
  }
  
  // 默认服务器错误
  res.status(500).json({
    code: 500,
    message: '服务器内部错误'
  })
}
```

## 7. 日志和监控

### 7.1 日志配置
```typescript
// utils/logger.ts
import winston from 'winston'

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
})
```

### 7.2 性能监控
- **响应时间监控**: 记录API响应时间
- **数据库查询监控**: 监控慢查询
- **内存使用监控**: 防止内存泄漏
- **错误率监控**: 统计错误发生率

## 8. 安全措施

### 8.1 输入验证
- 使用Joi进行参数验证
- SQL注入防护
- XSS攻击防护

### 8.2 访问控制
- JWT令牌验证
- 角色权限控制
- API访问频率限制

### 8.3 数据保护
- 敏感数据加密存储
- HTTPS传输加密
- 数据脱敏处理
