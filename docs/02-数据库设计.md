# 数据库设计文档

## 1. 数据库概述

本项目使用MySQL作为主数据库，Redis作为缓存层。数据库设计遵循第三范式，确保数据一致性和完整性。

## 2. 数据库表设计

### 2.1 拼图主表 (puzzles)

存储拼图的基本信息和配置。

```sql
CREATE TABLE puzzles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '拼图名称',
    description TEXT COMMENT '拼图描述',
    original_image_url VARCHAR(500) NOT NULL COMMENT '原图URL',
    piece_count INT NOT NULL DEFAULT 9 COMMENT '碎片数量',
    grid_rows INT NOT NULL DEFAULT 3 COMMENT '网格行数',
    grid_cols INT NOT NULL DEFAULT 3 COMMENT '网格列数',
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active' COMMENT '状态',
    start_time DATETIME COMMENT '活动开始时间',
    end_time DATETIME COMMENT '活动结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status_time (status, start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拼图主表';
```

### 2.2 拼图碎片表 (puzzle_pieces)

存储每个拼图的碎片信息。

```sql
CREATE TABLE puzzle_pieces (
    id INT PRIMARY KEY AUTO_INCREMENT,
    puzzle_id INT NOT NULL,
    piece_no INT NOT NULL COMMENT '碎片编号(1-N)',
    image_url VARCHAR(500) NOT NULL COMMENT '碎片图片URL',
    position_x INT NOT NULL COMMENT '在拼图中的X坐标',
    position_y INT NOT NULL COMMENT '在拼图中的Y坐标',
    width INT NOT NULL COMMENT '碎片宽度',
    height INT NOT NULL COMMENT '碎片高度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (puzzle_id) REFERENCES puzzles(id) ON DELETE CASCADE,
    UNIQUE KEY uk_puzzle_piece (puzzle_id, piece_no),
    INDEX idx_puzzle_id (puzzle_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拼图碎片表';
```

### 2.3 二维码表 (qrcodes)

存储二维码信息和状态。

```sql
CREATE TABLE qrcodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    puzzle_id INT NOT NULL,
    piece_no INT NOT NULL COMMENT '对应的碎片编号',
    qr_token VARCHAR(100) NOT NULL UNIQUE COMMENT '二维码唯一标识',
    qr_content TEXT NOT NULL COMMENT '二维码内容(包含签名)',
    is_used BOOLEAN DEFAULT FALSE COMMENT '是否已被使用',
    used_by VARCHAR(100) COMMENT '使用者标识(openid或设备ID)',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    expire_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (puzzle_id) REFERENCES puzzles(id) ON DELETE CASCADE,
    INDEX idx_puzzle_piece (puzzle_id, piece_no),
    INDEX idx_token (qr_token),
    INDEX idx_expire (expire_at),
    INDEX idx_used (is_used, used_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='二维码表';
```

### 2.4 用户状态表 (user_puzzle_states)

存储用户的拼图进度状态。

```sql
CREATE TABLE user_puzzle_states (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(100) NOT NULL COMMENT '用户标识(openid或设备ID)',
    puzzle_id INT NOT NULL,
    unlocked_pieces JSON NOT NULL COMMENT '已解锁的碎片编号列表',
    unlock_count INT NOT NULL DEFAULT 0 COMMENT '已解锁碎片数量',
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否完成拼图',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    first_unlock_at TIMESTAMP NULL COMMENT '首次解锁时间',
    last_unlock_at TIMESTAMP NULL COMMENT '最后解锁时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (puzzle_id) REFERENCES puzzles(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_puzzle (user_id, puzzle_id),
    INDEX idx_user_id (user_id),
    INDEX idx_completed (is_completed, completed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户拼图状态表';
```

### 2.5 奖品表 (prizes)

存储奖品信息。

```sql
CREATE TABLE prizes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    puzzle_id INT NOT NULL,
    name VARCHAR(100) NOT NULL COMMENT '奖品名称',
    description TEXT COMMENT '奖品描述',
    prize_type ENUM('code', 'coupon', 'gift') DEFAULT 'code' COMMENT '奖品类型',
    total_stock INT NOT NULL DEFAULT 0 COMMENT '总库存',
    remaining_stock INT NOT NULL DEFAULT 0 COMMENT '剩余库存',
    prize_value DECIMAL(10,2) COMMENT '奖品价值',
    redeem_address TEXT COMMENT '兑奖地址',
    expire_hours INT NOT NULL DEFAULT 24 COMMENT '兑换码有效期(小时)',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (puzzle_id) REFERENCES puzzles(id) ON DELETE CASCADE,
    INDEX idx_puzzle_status (puzzle_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖品表';
```

### 2.6 兑换码表 (redeem_codes)

存储生成的兑换码。

```sql
CREATE TABLE redeem_codes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '兑换码',
    user_id VARCHAR(100) NOT NULL COMMENT '用户标识',
    puzzle_id INT NOT NULL,
    prize_id INT NOT NULL,
    status ENUM('active', 'used', 'expired') DEFAULT 'active',
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
    expire_at TIMESTAMP NOT NULL COMMENT '过期时间',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    
    FOREIGN KEY (puzzle_id) REFERENCES puzzles(id) ON DELETE CASCADE,
    FOREIGN KEY (prize_id) REFERENCES prizes(id) ON DELETE CASCADE,
    INDEX idx_code (code),
    INDEX idx_user_puzzle (user_id, puzzle_id),
    INDEX idx_status_expire (status, expire_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='兑换码表';
```

### 2.7 操作日志表 (operation_logs)

记录关键操作的日志。

```sql
CREATE TABLE operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(100) COMMENT '用户标识',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id VARCHAR(100) COMMENT '资源ID',
    details JSON COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_action (user_id, action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

## 3. Redis缓存设计

### 3.1 缓存键命名规范

```
puzzle:{puzzle_id}:info                    # 拼图基本信息
puzzle:{puzzle_id}:pieces                  # 拼图碎片信息
user:{user_id}:puzzle:{puzzle_id}:state    # 用户拼图状态
qrcode:{qr_token}:info                     # 二维码信息
rate_limit:scan:{user_id}                  # 扫码频率限制
rate_limit:ip:{ip_address}                 # IP频率限制
stats:puzzle:{puzzle_id}:daily:{date}      # 每日统计数据
```

### 3.2 缓存策略

#### 3.2.1 拼图信息缓存
- **TTL**: 1小时
- **更新策略**: 写入时更新，定时刷新
- **数据结构**: Hash

#### 3.2.2 用户状态缓存
- **TTL**: 30分钟
- **更新策略**: 每次状态变更时更新
- **数据结构**: Hash

#### 3.2.3 频率限制缓存
- **TTL**: 根据限制策略设定
- **数据结构**: String (计数器)

## 4. 数据库索引优化

### 4.1 主要查询场景
1. 根据二维码token查询碎片信息
2. 查询用户的拼图状态
3. 统计拼图完成情况
4. 查询可用的奖品库存

### 4.2 索引设计原则
- 高频查询字段建立索引
- 复合索引遵循最左前缀原则
- 避免过多索引影响写入性能
- 定期分析索引使用情况

## 5. 数据备份和恢复

### 5.1 备份策略
- **全量备份**: 每日凌晨执行
- **增量备份**: 每小时执行
- **备份保留**: 全量备份保留30天，增量备份保留7天

### 5.2 恢复策略
- **故障恢复**: 自动切换到备库
- **数据恢复**: 基于备份文件的时间点恢复
- **灾难恢复**: 异地备份和多活部署

## 6. 数据库监控

### 6.1 监控指标
- 连接数和活跃连接
- 查询响应时间
- 慢查询统计
- 锁等待和死锁
- 缓存命中率

### 6.2 告警规则
- 连接数超过阈值
- 慢查询数量异常
- 主从延迟过大
- 磁盘空间不足
