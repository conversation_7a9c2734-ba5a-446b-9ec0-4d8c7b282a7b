# 前端技术方案文档

## 1. 技术栈选择

### 1.1 核心框架
- **Vue.js 3**: 采用Composition API，更好的TypeScript支持
- **TypeScript**: 提供类型安全，提高代码质量
- **Vite**: 快速的构建工具，支持热更新和模块化

### 1.2 UI组件库
- **Vant 4**: 专为移动端设计的Vue组件库
- **自定义组件**: 拼图相关的特殊组件

### 1.3 状态管理
- **Pinia**: Vue 3官方推荐的状态管理库
- **持久化**: 使用localStorage存储用户状态

### 1.4 工具库
- **Axios**: HTTP客户端，支持请求拦截和响应处理
- **Day.js**: 轻量级日期处理库
- **Lodash**: 实用工具函数库
- **WeChat JS-SDK**: 微信功能集成

## 2. 项目结构

```
src/
├── assets/                 # 静态资源
│   ├── images/            # 图片资源
│   ├── styles/            # 样式文件
│   └── fonts/             # 字体文件
├── components/            # 公共组件
│   ├── PuzzleBoard/       # 拼图面板组件
│   ├── PuzzlePiece/       # 拼图碎片组件
│   ├── QRScanner/         # 二维码扫描组件
│   └── PrizeModal/        # 奖品弹窗组件
├── views/                 # 页面组件
│   ├── Home/              # 首页
│   ├── Puzzle/            # 拼图页面
│   └── Prize/             # 奖品页面
├── stores/                # 状态管理
│   ├── puzzle.ts          # 拼图状态
│   ├── user.ts            # 用户状态
│   └── app.ts             # 应用状态
├── services/              # API服务
│   ├── api.ts             # API配置
│   ├── puzzle.ts          # 拼图相关API
│   ├── qrcode.ts          # 二维码相关API
│   └── prize.ts           # 奖品相关API
├── utils/                 # 工具函数
│   ├── request.ts         # 请求封装
│   ├── storage.ts         # 存储工具
│   ├── wechat.ts          # 微信工具
│   └── common.ts          # 通用工具
├── types/                 # 类型定义
│   ├── puzzle.ts          # 拼图类型
│   ├── user.ts            # 用户类型
│   └── api.ts             # API类型
└── router/                # 路由配置
    └── index.ts
```

## 3. 核心组件设计

### 3.1 拼图面板组件 (PuzzleBoard)

```typescript
// components/PuzzleBoard/index.vue
<template>
  <div class="puzzle-board" :style="boardStyle">
    <div 
      v-for="position in totalPositions" 
      :key="position"
      class="puzzle-slot"
      :style="getSlotStyle(position)"
    >
      <PuzzlePiece 
        v-if="getPieceByPosition(position)"
        :piece="getPieceByPosition(position)"
        :is-unlocked="isPieceUnlocked(position)"
        @click="onPieceClick"
      />
      <div v-else class="empty-slot">
        <van-icon name="plus" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  puzzle: PuzzleInfo
  unlockedPieces: number[]
}

interface PuzzleInfo {
  id: number
  gridRows: number
  gridCols: number
  pieces: PuzzlePiece[]
}

const props = defineProps<Props>()

const boardStyle = computed(() => ({
  gridTemplateRows: `repeat(${props.puzzle.gridRows}, 1fr)`,
  gridTemplateColumns: `repeat(${props.puzzle.gridCols}, 1fr)`
}))
</script>
```

### 3.2 二维码扫描组件 (QRScanner)

```typescript
// components/QRScanner/index.vue
<template>
  <div class="qr-scanner">
    <van-button 
      type="primary" 
      size="large"
      @click="startScan"
      :loading="scanning"
    >
      {{ scanning ? '扫描中...' : '扫描二维码' }}
    </van-button>
  </div>
</template>

<script setup lang="ts">
import { useWechat } from '@/utils/wechat'

const emit = defineEmits<{
  scan: [result: string]
  error: [error: string]
}>()

const { isWechat, scanQRCode } = useWechat()
const scanning = ref(false)

const startScan = async () => {
  if (!isWechat.value) {
    emit('error', '请在微信中打开')
    return
  }

  scanning.value = true
  try {
    const result = await scanQRCode()
    emit('scan', result)
  } catch (error) {
    emit('error', '扫描失败')
  } finally {
    scanning.value = false
  }
}
</script>
```

### 3.3 奖品弹窗组件 (PrizeModal)

```typescript
// components/PrizeModal/index.vue
<template>
  <van-dialog
    v-model:show="visible"
    title="恭喜完成拼图！"
    :show-cancel-button="false"
    confirm-button-text="我知道了"
    @confirm="onConfirm"
  >
    <div class="prize-content">
      <div class="completed-image">
        <img :src="completedImageUrl" alt="完成的拼图" />
      </div>
      <div class="prize-info">
        <h3>{{ prizeInfo.name }}</h3>
        <p>{{ prizeInfo.description }}</p>
        <div class="redeem-code">
          <span>兑换码：{{ prizeInfo.redeemCode }}</span>
          <van-button size="mini" @click="copyCode">复制</van-button>
        </div>
        <div class="redeem-address">
          <p>领奖地址：{{ prizeInfo.redeemAddress }}</p>
        </div>
        <div class="expire-time">
          <p>有效期至：{{ formatExpireTime(prizeInfo.expireAt) }}</p>
        </div>
      </div>
    </div>
  </van-dialog>
</template>
```

## 4. 状态管理设计

### 4.1 拼图状态 (stores/puzzle.ts)

```typescript
import { defineStore } from 'pinia'

export const usePuzzleStore = defineStore('puzzle', () => {
  // 状态
  const currentPuzzle = ref<PuzzleInfo | null>(null)
  const unlockedPieces = ref<number[]>([])
  const isCompleted = ref(false)
  const loading = ref(false)

  // 计算属性
  const progress = computed(() => {
    if (!currentPuzzle.value) return 0
    return unlockedPieces.value.length / currentPuzzle.value.pieceCount
  })

  const completionPercentage = computed(() => {
    return Math.round(progress.value * 100)
  })

  // 方法
  const loadPuzzle = async (puzzleId: number) => {
    loading.value = true
    try {
      const puzzle = await puzzleApi.getPuzzle(puzzleId)
      const userState = await puzzleApi.getUserState(puzzleId)
      
      currentPuzzle.value = puzzle
      unlockedPieces.value = userState.unlockedPieces
      isCompleted.value = userState.isCompleted
    } finally {
      loading.value = false
    }
  }

  const unlockPiece = (pieceNo: number) => {
    if (!unlockedPieces.value.includes(pieceNo)) {
      unlockedPieces.value.push(pieceNo)
      
      // 检查是否完成
      if (currentPuzzle.value && 
          unlockedPieces.value.length === currentPuzzle.value.pieceCount) {
        isCompleted.value = true
      }
    }
  }

  // 持久化
  const persistState = () => {
    if (currentPuzzle.value) {
      localStorage.setItem('puzzle_state', JSON.stringify({
        puzzleId: currentPuzzle.value.id,
        unlockedPieces: unlockedPieces.value,
        isCompleted: isCompleted.value
      }))
    }
  }

  const restoreState = () => {
    const saved = localStorage.getItem('puzzle_state')
    if (saved) {
      const state = JSON.parse(saved)
      unlockedPieces.value = state.unlockedPieces || []
      isCompleted.value = state.isCompleted || false
    }
  }

  return {
    currentPuzzle,
    unlockedPieces,
    isCompleted,
    loading,
    progress,
    completionPercentage,
    loadPuzzle,
    unlockPiece,
    persistState,
    restoreState
  }
})
```

## 5. 微信集成方案

### 5.1 微信JS-SDK配置

```typescript
// utils/wechat.ts
import wx from 'weixin-js-sdk'

export const useWechat = () => {
  const isWechat = computed(() => {
    return /MicroMessenger/i.test(navigator.userAgent)
  })

  const configWechat = async () => {
    if (!isWechat.value) return

    try {
      const config = await api.getWechatConfig(location.href)
      
      wx.config({
        debug: false,
        appId: config.appId,
        timestamp: config.timestamp,
        nonceStr: config.nonceStr,
        signature: config.signature,
        jsApiList: ['scanQRCode', 'onMenuShareTimeline', 'onMenuShareAppMessage']
      })

      return new Promise((resolve, reject) => {
        wx.ready(resolve)
        wx.error(reject)
      })
    } catch (error) {
      console.error('微信配置失败:', error)
      throw error
    }
  }

  const scanQRCode = (): Promise<string> => {
    return new Promise((resolve, reject) => {
      wx.scanQRCode({
        needResult: 1,
        scanType: ['qrCode'],
        success: (res) => {
          resolve(res.resultStr)
        },
        fail: reject
      })
    })
  }

  return {
    isWechat,
    configWechat,
    scanQRCode
  }
}
```

## 6. 性能优化

### 6.1 图片优化
- 使用WebP格式，降级到JPEG
- 实现图片懒加载
- 预加载关键图片资源

### 6.2 代码分割
- 路由级别的代码分割
- 组件按需加载
- 第三方库按需引入

### 6.3 缓存策略
- 静态资源长期缓存
- API响应适当缓存
- 用户状态本地存储

## 7. 错误处理

### 7.1 全局错误处理
```typescript
// main.ts
app.config.errorHandler = (error, instance, info) => {
  console.error('全局错误:', error, info)
  // 上报错误到监控系统
  reportError(error, info)
}
```

### 7.2 API错误处理
```typescript
// utils/request.ts
axios.interceptors.response.use(
  response => response,
  error => {
    const { response } = error
    
    if (response?.status === 429) {
      showToast('操作过于频繁，请稍后再试')
    } else if (response?.status >= 500) {
      showToast('服务器错误，请稍后再试')
    }
    
    return Promise.reject(error)
  }
)
```

## 8. 测试策略

### 8.1 单元测试
- 使用Vitest进行单元测试
- 组件测试使用Vue Test Utils
- 工具函数100%覆盖率

### 8.2 端到端测试
- 使用Playwright进行E2E测试
- 覆盖主要用户流程
- 自动化回归测试
