# 系统架构设计文档

## 1. 项目概述

本项目是一个基于二维码扫描的拼图游戏系统，用户通过扫描不同的二维码来解锁拼图碎片，完成拼图后获得奖品兑换码。

## 2. 整体架构

### 2.1 架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信/浏览器    │    │     CDN         │    │   负载均衡器     │
│   (H5前端)      │◄──►│   (静态资源)     │◄──►│   (Nginx)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │                                              ▼
         │                                    ┌─────────────────┐
         │                                    │   Web服务器      │
         │                                    │   (Node.js)     │
         │                                    └─────────────────┘
         │                                              │
         │                                              ▼
         │                                    ┌─────────────────┐
         │                                    │   应用服务器     │
         │                                    │   (Express.js)  │
         │                                    └─────────────────┘
         │                                              │
         │                                              ▼
         │                                    ┌─────────────────┐
         │                                    │   缓存层        │
         │                                    │   (Redis)       │
         │                                    └─────────────────┘
         │                                              │
         │                                              ▼
         │                                    ┌─────────────────┐
         │                                    │   数据库        │
         │                                    │   (MySQL)       │
         │                                    └─────────────────┘
         │
         ▼
┌─────────────────┐
│   文件存储       │
│   (OSS/S3)      │
└─────────────────┘
```

### 2.2 技术栈选择

#### 前端技术栈
- **框架**: Vue.js 3 + TypeScript
- **构建工具**: Vite
- **UI组件**: <PERSON><PERSON> (移动端UI库)
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **微信集成**: WeChat JS-SDK

#### 后端技术栈
- **运行环境**: Node.js 18+
- **Web框架**: Express.js
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0
- **ORM**: Prisma
- **认证**: JWT
- **图片处理**: Sharp

#### 基础设施
- **Web服务器**: Nginx
- **容器化**: Docker + Docker Compose
- **文件存储**: 阿里云OSS / AWS S3
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 3. 系统模块划分

### 3.1 前端模块
- **拼图展示模块**: 负责拼图界面渲染和交互
- **扫码模块**: 集成微信扫码功能
- **状态管理模块**: 管理用户解锁状态
- **奖品展示模块**: 显示兑换码和领奖信息

### 3.2 后端模块
- **二维码管理模块**: 二维码生成、验证、状态管理
- **拼图逻辑模块**: 拼图切片、状态计算
- **用户管理模块**: 用户身份识别和状态跟踪
- **奖品管理模块**: 兑换码生成和管理
- **防刷模块**: 防止恶意刷取和作弊

## 4. 数据流设计

### 4.1 用户扫码流程
```
用户扫码 → 前端获取二维码 → 后端验证 → 返回碎片信息 → 前端渲染碎片 → 更新状态
```

### 4.2 拼图完成流程
```
检查完成状态 → 生成兑换码 → 绑定用户身份 → 返回兑换信息 → 前端展示
```

## 5. 安全设计

### 5.1 二维码安全
- 使用JWT签名验证二维码合法性
- 二维码包含时间戳防止重放攻击
- 每个二维码只能被首次扫描者解锁

### 5.2 防刷机制
- Redis计数器限制扫码频率
- IP限流防止批量扫码
- 用户身份绑定防止多账号刷取

### 5.3 数据安全
- 敏感数据加密存储
- API接口鉴权
- HTTPS传输加密

## 6. 性能优化

### 6.1 前端优化
- 图片懒加载和预加载
- 组件按需加载
- 本地存储缓存用户状态
- CDN加速静态资源

### 6.2 后端优化
- Redis缓存热点数据
- 数据库索引优化
- 连接池管理
- 异步处理非关键操作

## 7. 扩展性设计

### 7.1 水平扩展
- 无状态应用服务器设计
- 数据库读写分离
- 缓存集群部署

### 7.2 功能扩展
- 支持多种拼图规格(4/9/16片)
- 支持多主题拼图
- 支持动画和音效
- 运营后台管理系统

## 8. 监控和运维

### 8.1 监控指标
- 系统性能指标(CPU、内存、磁盘)
- 业务指标(扫码次数、完成率、兑换率)
- 错误监控和告警

### 8.2 日志管理
- 结构化日志记录
- 日志分级和轮转
- 集中化日志收集和分析

## 9. 部署架构

### 9.1 环境划分
- **开发环境**: 本地开发和单元测试
- **测试环境**: 集成测试和功能测试
- **预生产环境**: 性能测试和压力测试
- **生产环境**: 正式运行环境

### 9.2 容器化部署
- Docker镜像构建
- Docker Compose本地开发
- Kubernetes生产部署
- CI/CD自动化流水线
