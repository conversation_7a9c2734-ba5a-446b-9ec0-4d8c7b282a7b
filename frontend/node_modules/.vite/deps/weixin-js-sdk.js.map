{"version": 3, "sources": ["../../weixin-js-sdk/index.js"], "sourcesContent": ["!(function (e, n) {\n  module.exports = n(e);\n})(typeof window === \"object\" && window, function (r, e) {\n  if (!r) {\n    console.warn(\"can't use weixin-js-sdk in server side\");\n    return;\n  }\n  var a, c, n, i, t, o, s, d, l, u, p, f, m, g, h, S, y, I, v, _, w, T;\n  if (!r.jWeixin)\n    return (\n      (a = {\n        config: \"preVerifyJSAPI\",\n        onMenuShareTimeline: \"menu:share:timeline\",\n        onMenuShareAppMessage: \"menu:share:appmessage\",\n        onMenuShareQQ: \"menu:share:qq\",\n        onMenuShareWeibo: \"menu:share:weiboApp\",\n        onMenuShareQZone: \"menu:share:QZone\",\n        previewImage: \"imagePreview\",\n        getLocation: \"geoLocation\",\n        openProductSpecificView: \"openProductViewWithPid\",\n        addCard: \"batchAddCard\",\n        openCard: \"batchViewCard\",\n        chooseWXPay: \"getBrandWCPayRequest\",\n        openEnterpriseRedPacket: \"getRecevieBizHongBaoRequest\",\n        startSearchBeacons: \"startMonitoringBeacons\",\n        stopSearchBeacons: \"stopMonitoringBeacons\",\n        onSearchBeacons: \"onBeaconsInRange\",\n        consumeAndShareCard: \"consumedShareCard\",\n        openAddress: \"editAddress\",\n      }),\n      (c = (function () {\n        var e,\n          n = {};\n        for (e in a) n[a[e]] = e;\n        return n;\n      })()),\n      (n = r.document),\n      (i = n.title),\n      (t = navigator.userAgent.toLowerCase()),\n      (f = navigator.platform.toLowerCase()),\n      (o = !(!f.match(\"mac\") && !f.match(\"win\"))),\n      (s = -1 != t.indexOf(\"wxdebugger\")),\n      (d = -1 != t.indexOf(\"micromessenger\")),\n      (l = -1 != t.indexOf(\"android\")),\n      (u = -1 != t.indexOf(\"iphone\") || -1 != t.indexOf(\"ipad\")),\n      (p = (f =\n        t.match(/micromessenger\\/(\\d+\\.\\d+\\.\\d+)/) ||\n        t.match(/micromessenger\\/(\\d+\\.\\d+)/))\n        ? f[1]\n        : \"\"),\n      (m = {\n        initStartTime: L(),\n        initEndTime: 0,\n        preVerifyStartTime: 0,\n        preVerifyEndTime: 0,\n      }),\n      (g = {\n        version: 1,\n        appId: \"\",\n        initTime: 0,\n        preVerifyTime: 0,\n        networkType: \"\",\n        isPreVerifyOk: 1,\n        systemType: u ? 1 : l ? 2 : -1,\n        clientVersion: p,\n        url: encodeURIComponent(location.href),\n      }),\n      (h = {}),\n      (S = { _completes: [] }),\n      (y = { state: 0, data: {} }),\n      O(function () {\n        m.initEndTime = L();\n      }),\n      (I = !1),\n      (v = []),\n      (_ = {\n        config: function (e) {\n          C(\"config\", (h = e));\n          var o = !1 !== h.check;\n          O(function () {\n            if (o)\n              k(\n                a.config,\n                {\n                  verifyJsApiList: A(h.jsApiList),\n                  verifyOpenTagList: A(h.openTagList),\n                },\n                ((S._complete = function (e) {\n                  (m.preVerifyEndTime = L()), (y.state = 1), (y.data = e);\n                }),\n                (S.success = function (e) {\n                  g.isPreVerifyOk = 0;\n                }),\n                (S.fail = function (e) {\n                  S._fail ? S._fail(e) : (y.state = -1);\n                }),\n                (t = S._completes).push(function () {\n                  B();\n                }),\n                (S.complete = function (e) {\n                  for (var n = 0, i = t.length; n < i; ++n) t[n]();\n                  S._completes = [];\n                }),\n                S)\n              ),\n                (m.preVerifyStartTime = L());\n            else {\n              y.state = 1;\n              for (var e = S._completes, n = 0, i = e.length; n < i; ++n)\n                e[n]();\n              S._completes = [];\n            }\n            var t;\n          }),\n            _.invoke ||\n              ((_.invoke = function (e, n, i) {\n                r.WeixinJSBridge && WeixinJSBridge.invoke(e, P(n), i);\n              }),\n              (_.on = function (e, n) {\n                r.WeixinJSBridge && WeixinJSBridge.on(e, n);\n              }));\n        },\n        ready: function (e) {\n          (0 != y.state || (S._completes.push(e), !d && h.debug)) && e();\n        },\n        error: function (e) {\n          p < \"6.0.2\" || (-1 == y.state ? e(y.data) : (S._fail = e));\n        },\n        checkJsApi: function (e) {\n          k(\n            \"checkJsApi\",\n            { jsApiList: A(e.jsApiList) },\n            ((e._complete = function (e) {\n              l && (i = e.checkResult) && (e.checkResult = JSON.parse(i));\n              var n,\n                i = e,\n                t = i.checkResult;\n              for (n in t) {\n                var o = c[n];\n                o && ((t[o] = t[n]), delete t[n]);\n              }\n            }),\n            e)\n          );\n        },\n        onMenuShareTimeline: function (e) {\n          M(\n            a.onMenuShareTimeline,\n            {\n              complete: function () {\n                k(\n                  \"shareTimeline\",\n                  {\n                    title: e.title || i,\n                    desc: e.title || i,\n                    img_url: e.imgUrl || \"\",\n                    link: e.link || location.href,\n                    type: e.type || \"link\",\n                    data_url: e.dataUrl || \"\",\n                  },\n                  e\n                );\n              },\n            },\n            e\n          );\n        },\n        onMenuShareAppMessage: function (n) {\n          M(\n            a.onMenuShareAppMessage,\n            {\n              complete: function (e) {\n                \"favorite\" === e.scene\n                  ? k(\"sendAppMessage\", {\n                      title: n.title || i,\n                      desc: n.desc || \"\",\n                      link: n.link || location.href,\n                      img_url: n.imgUrl || \"\",\n                      type: n.type || \"link\",\n                      data_url: n.dataUrl || \"\",\n                    })\n                  : k(\n                      \"sendAppMessage\",\n                      {\n                        title: n.title || i,\n                        desc: n.desc || \"\",\n                        link: n.link || location.href,\n                        img_url: n.imgUrl || \"\",\n                        type: n.type || \"link\",\n                        data_url: n.dataUrl || \"\",\n                      },\n                      n\n                    );\n              },\n            },\n            n\n          );\n        },\n        onMenuShareQQ: function (e) {\n          M(\n            a.onMenuShareQQ,\n            {\n              complete: function () {\n                k(\n                  \"shareQQ\",\n                  {\n                    title: e.title || i,\n                    desc: e.desc || \"\",\n                    img_url: e.imgUrl || \"\",\n                    link: e.link || location.href,\n                  },\n                  e\n                );\n              },\n            },\n            e\n          );\n        },\n        onMenuShareWeibo: function (e) {\n          M(\n            a.onMenuShareWeibo,\n            {\n              complete: function () {\n                k(\n                  \"shareWeiboApp\",\n                  {\n                    title: e.title || i,\n                    desc: e.desc || \"\",\n                    img_url: e.imgUrl || \"\",\n                    link: e.link || location.href,\n                  },\n                  e\n                );\n              },\n            },\n            e\n          );\n        },\n        onMenuShareQZone: function (e) {\n          M(\n            a.onMenuShareQZone,\n            {\n              complete: function () {\n                k(\n                  \"shareQZone\",\n                  {\n                    title: e.title || i,\n                    desc: e.desc || \"\",\n                    img_url: e.imgUrl || \"\",\n                    link: e.link || location.href,\n                  },\n                  e\n                );\n              },\n            },\n            e\n          );\n        },\n        updateTimelineShareData: function (e) {\n          k(\n            \"updateTimelineShareData\",\n            { title: e.title, link: e.link, imgUrl: e.imgUrl },\n            e\n          );\n        },\n        updateAppMessageShareData: function (e) {\n          k(\n            \"updateAppMessageShareData\",\n            { title: e.title, desc: e.desc, link: e.link, imgUrl: e.imgUrl },\n            e\n          );\n        },\n        startRecord: function (e) {\n          k(\"startRecord\", {}, e);\n        },\n        stopRecord: function (e) {\n          k(\"stopRecord\", {}, e);\n        },\n        onVoiceRecordEnd: function (e) {\n          M(\"onVoiceRecordEnd\", e);\n        },\n        playVoice: function (e) {\n          k(\"playVoice\", { localId: e.localId }, e);\n        },\n        pauseVoice: function (e) {\n          k(\"pauseVoice\", { localId: e.localId }, e);\n        },\n        stopVoice: function (e) {\n          k(\"stopVoice\", { localId: e.localId }, e);\n        },\n        onVoicePlayEnd: function (e) {\n          M(\"onVoicePlayEnd\", e);\n        },\n        uploadVoice: function (e) {\n          k(\n            \"uploadVoice\",\n            {\n              localId: e.localId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1,\n            },\n            e\n          );\n        },\n        downloadVoice: function (e) {\n          k(\n            \"downloadVoice\",\n            {\n              serverId: e.serverId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1,\n            },\n            e\n          );\n        },\n        translateVoice: function (e) {\n          k(\n            \"translateVoice\",\n            {\n              localId: e.localId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1,\n            },\n            e\n          );\n        },\n        chooseImage: function (e) {\n          k(\n            \"chooseImage\",\n            {\n              scene: \"1|2\",\n              count: e.count || 9,\n              sizeType: e.sizeType || [\"original\", \"compressed\"],\n              sourceType: e.sourceType || [\"album\", \"camera\"],\n            },\n            ((e._complete = function (e) {\n              if (l) {\n                var n = e.localIds;\n                try {\n                  n && (e.localIds = JSON.parse(n));\n                } catch (e) {}\n              }\n            }),\n            e)\n          );\n        },\n        getLocation: function (e) {\n          (e = e || {}),\n            k(\n              a.getLocation,\n              { type: e.type || \"wgs84\" },\n              ((e._complete = function (e) {\n                delete e.type;\n              }),\n              e)\n            );\n        },\n        previewImage: function (e) {\n          k(a.previewImage, { current: e.current, urls: e.urls }, e);\n        },\n        uploadImage: function (e) {\n          k(\n            \"uploadImage\",\n            {\n              localId: e.localId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1,\n            },\n            e\n          );\n        },\n        downloadImage: function (e) {\n          k(\n            \"downloadImage\",\n            {\n              serverId: e.serverId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1,\n            },\n            e\n          );\n        },\n        getLocalImgData: function (e) {\n          !1 === I\n            ? ((I = !0),\n              k(\n                \"getLocalImgData\",\n                { localId: e.localId },\n                ((e._complete = function (e) {\n                  var n;\n                  (I = !1),\n                    0 < v.length && ((n = v.shift()), wx.getLocalImgData(n));\n                }),\n                e)\n              ))\n            : v.push(e);\n        },\n        getNetworkType: function (e) {\n          k(\n            \"getNetworkType\",\n            {},\n            ((e._complete = function (e) {\n              var n = e,\n                e = n.errMsg,\n                i = ((n.errMsg = \"getNetworkType:ok\"), n.subtype);\n              if ((delete n.subtype, i)) n.networkType = i;\n              else {\n                var i = e.indexOf(\":\"),\n                  t = e.substring(i + 1);\n                switch (t) {\n                  case \"wifi\":\n                  case \"edge\":\n                  case \"wwan\":\n                    n.networkType = t;\n                    break;\n                  default:\n                    n.errMsg = \"getNetworkType:fail\";\n                }\n              }\n            }),\n            e)\n          );\n        },\n        openLocation: function (e) {\n          k(\n            \"openLocation\",\n            {\n              latitude: e.latitude,\n              longitude: e.longitude,\n              name: e.name || \"\",\n              address: e.address || \"\",\n              scale: e.scale || 28,\n              infoUrl: e.infoUrl || \"\",\n            },\n            e\n          );\n        },\n        hideOptionMenu: function (e) {\n          k(\"hideOptionMenu\", {}, e);\n        },\n        showOptionMenu: function (e) {\n          k(\"showOptionMenu\", {}, e);\n        },\n        closeWindow: function (e) {\n          k(\"closeWindow\", {}, (e = e || {}));\n        },\n        hideMenuItems: function (e) {\n          k(\"hideMenuItems\", { menuList: e.menuList }, e);\n        },\n        showMenuItems: function (e) {\n          k(\"showMenuItems\", { menuList: e.menuList }, e);\n        },\n        hideAllNonBaseMenuItem: function (e) {\n          k(\"hideAllNonBaseMenuItem\", {}, e);\n        },\n        showAllNonBaseMenuItem: function (e) {\n          k(\"showAllNonBaseMenuItem\", {}, e);\n        },\n        scanQRCode: function (e) {\n          k(\n            \"scanQRCode\",\n            {\n              needResult: (e = e || {}).needResult || 0,\n              scanType: e.scanType || [\"qrCode\", \"barCode\"],\n            },\n            ((e._complete = function (e) {\n              var n;\n              u &&\n                (n = e.resultStr) &&\n                ((n = JSON.parse(n)),\n                (e.resultStr = n && n.scan_code && n.scan_code.scan_result));\n            }),\n            e)\n          );\n        },\n        openAddress: function (e) {\n          k(\n            a.openAddress,\n            {},\n            ((e._complete = function (e) {\n              ((e = e).postalCode = e.addressPostalCode),\n                delete e.addressPostalCode,\n                (e.provinceName = e.proviceFirstStageName),\n                delete e.proviceFirstStageName,\n                (e.cityName = e.addressCitySecondStageName),\n                delete e.addressCitySecondStageName,\n                (e.countryName = e.addressCountiesThirdStageName),\n                delete e.addressCountiesThirdStageName,\n                (e.detailInfo = e.addressDetailInfo),\n                delete e.addressDetailInfo;\n            }),\n            e)\n          );\n        },\n        openProductSpecificView: function (e) {\n          k(\n            a.openProductSpecificView,\n            {\n              pid: e.productId,\n              view_type: e.viewType || 0,\n              ext_info: e.extInfo,\n            },\n            e\n          );\n        },\n        addCard: function (e) {\n          for (var n = e.cardList, i = [], t = 0, o = n.length; t < o; ++t) {\n            var r = n[t],\n              r = { card_id: r.cardId, card_ext: r.cardExt };\n            i.push(r);\n          }\n          k(\n            a.addCard,\n            { card_list: i },\n            ((e._complete = function (e) {\n              if ((n = e.card_list)) {\n                for (var n, i = 0, t = (n = JSON.parse(n)).length; i < t; ++i) {\n                  var o = n[i];\n                  (o.cardId = o.card_id),\n                    (o.cardExt = o.card_ext),\n                    (o.isSuccess = !!o.is_succ),\n                    delete o.card_id,\n                    delete o.card_ext,\n                    delete o.is_succ;\n                }\n                (e.cardList = n), delete e.card_list;\n              }\n            }),\n            e)\n          );\n        },\n        chooseCard: function (e) {\n          k(\n            \"chooseCard\",\n            {\n              app_id: h.appId,\n              location_id: e.shopId || \"\",\n              sign_type: e.signType || \"SHA1\",\n              card_id: e.cardId || \"\",\n              card_type: e.cardType || \"\",\n              card_sign: e.cardSign,\n              time_stamp: e.timestamp + \"\",\n              nonce_str: e.nonceStr,\n            },\n            ((e._complete = function (e) {\n              (e.cardList = e.choose_card_info), delete e.choose_card_info;\n            }),\n            e)\n          );\n        },\n        openCard: function (e) {\n          for (var n = e.cardList, i = [], t = 0, o = n.length; t < o; ++t) {\n            var r = n[t],\n              r = { card_id: r.cardId, code: r.code };\n            i.push(r);\n          }\n          k(a.openCard, { card_list: i }, e);\n        },\n        consumeAndShareCard: function (e) {\n          k(\n            a.consumeAndShareCard,\n            { consumedCardId: e.cardId, consumedCode: e.code },\n            e\n          );\n        },\n        chooseWXPay: function (e) {\n          k(a.chooseWXPay, x(e), e), B({ jsApiName: \"chooseWXPay\" });\n        },\n        openEnterpriseRedPacket: function (e) {\n          k(a.openEnterpriseRedPacket, x(e), e);\n        },\n        startSearchBeacons: function (e) {\n          k(a.startSearchBeacons, { ticket: e.ticket }, e);\n        },\n        stopSearchBeacons: function (e) {\n          k(a.stopSearchBeacons, {}, e);\n        },\n        onSearchBeacons: function (e) {\n          M(a.onSearchBeacons, e);\n        },\n        openEnterpriseChat: function (e) {\n          k(\n            \"openEnterpriseChat\",\n            { useridlist: e.userIds, chatname: e.groupName },\n            e\n          );\n        },\n        launchMiniProgram: function (e) {\n          k(\n            \"launchMiniProgram\",\n            {\n              targetAppId: e.targetAppId,\n              path: (function (e) {\n                var n;\n                if (\"string\" == typeof e && 0 < e.length)\n                  return (\n                    (n = e.split(\"?\")[0]),\n                    (n += \".html\"),\n                    void 0 !== (e = e.split(\"?\")[1]) ? n + \"?\" + e : n\n                  );\n              })(e.path),\n              envVersion: e.envVersion,\n            },\n            e\n          );\n        },\n        openBusinessView: function (e) {\n          k(\n            \"openBusinessView\",\n            {\n              businessType: e.businessType,\n              queryString: e.queryString || \"\",\n              envVersion: e.envVersion,\n            },\n            ((e._complete = function (n) {\n              if (l) {\n                var e = n.extraData;\n                if (e)\n                  try {\n                    n.extraData = JSON.parse(e);\n                  } catch (e) {\n                    n.extraData = {};\n                  }\n              }\n            }),\n            e)\n          );\n        },\n        miniProgram: {\n          navigateBack: function (e) {\n            (e = e || {}),\n              O(function () {\n                k(\n                  \"invokeMiniProgramAPI\",\n                  { name: \"navigateBack\", arg: { delta: e.delta || 1 } },\n                  e\n                );\n              });\n          },\n          navigateTo: function (e) {\n            O(function () {\n              k(\n                \"invokeMiniProgramAPI\",\n                { name: \"navigateTo\", arg: { url: e.url } },\n                e\n              );\n            });\n          },\n          redirectTo: function (e) {\n            O(function () {\n              k(\n                \"invokeMiniProgramAPI\",\n                { name: \"redirectTo\", arg: { url: e.url } },\n                e\n              );\n            });\n          },\n          switchTab: function (e) {\n            O(function () {\n              k(\n                \"invokeMiniProgramAPI\",\n                { name: \"switchTab\", arg: { url: e.url } },\n                e\n              );\n            });\n          },\n          reLaunch: function (e) {\n            O(function () {\n              k(\n                \"invokeMiniProgramAPI\",\n                { name: \"reLaunch\", arg: { url: e.url } },\n                e\n              );\n            });\n          },\n          postMessage: function (e) {\n            O(function () {\n              k(\n                \"invokeMiniProgramAPI\",\n                { name: \"postMessage\", arg: e.data || {} },\n                e\n              );\n            });\n          },\n          getEnv: function (e) {\n            O(function () {\n              e({ miniprogram: \"miniprogram\" === r.__wxjs_environment });\n            });\n          },\n        },\n      }),\n      (w = 1),\n      (T = {}),\n      n.addEventListener(\n        \"error\",\n        function (e) {\n          var n, i, t;\n          l ||\n            ((t = (n = e.target).tagName),\n            (i = n.src),\n            \"IMG\" != t && \"VIDEO\" != t && \"AUDIO\" != t && \"SOURCE\" != t) ||\n            (-1 != i.indexOf(\"wxlocalresource://\") &&\n              (e.preventDefault(),\n              e.stopPropagation(),\n              (t = n[\"wx-id\"]) || ((t = w++), (n[\"wx-id\"] = t)),\n              T[t] ||\n                ((T[t] = !0),\n                wx.ready(function () {\n                  wx.getLocalImgData({\n                    localId: i,\n                    success: function (e) {\n                      n.src = e.localData;\n                    },\n                  });\n                }))));\n        },\n        !0\n      ),\n      n.addEventListener(\n        \"load\",\n        function (e) {\n          var n;\n          l ||\n            ((n = (e = e.target).tagName),\n            e.src,\n            \"IMG\" != n && \"VIDEO\" != n && \"AUDIO\" != n && \"SOURCE\" != n) ||\n            ((n = e[\"wx-id\"]) && (T[n] = !1));\n        },\n        !0\n      ),\n      e && (r.wx = r.jWeixin = _),\n      _\n    );\n  else return r.jWeixin;\n  function k(n, e, i) {\n    r.WeixinJSBridge\n      ? WeixinJSBridge.invoke(n, P(e), function (e) {\n          V(n, e, i);\n        })\n      : C(n, i);\n  }\n  function M(n, i, t) {\n    r.WeixinJSBridge\n      ? WeixinJSBridge.on(n, function (e) {\n          t && t.trigger && t.trigger(e), V(n, e, i);\n        })\n      : C(n, t || i);\n  }\n  function P(e) {\n    return (\n      ((e = e || {}).appId = h.appId),\n      (e.verifyAppId = h.appId),\n      (e.verifySignType = \"sha1\"),\n      (e.verifyTimestamp = h.timestamp + \"\"),\n      (e.verifyNonceStr = h.nonceStr),\n      (e.verifySignature = h.signature),\n      e\n    );\n  }\n  function x(e) {\n    return {\n      timeStamp: e.timestamp + \"\",\n      nonceStr: e.nonceStr,\n      package: e.package,\n      paySign: e.paySign,\n      signType: e.signType || \"SHA1\",\n    };\n  }\n  function V(e, n, i) {\n    (\"openEnterpriseChat\" != e && \"openBusinessView\" !== e) ||\n      (n.errCode = n.err_code),\n      delete n.err_code,\n      delete n.err_desc,\n      delete n.err_detail;\n    var t = n.errMsg,\n      e =\n        (t ||\n          ((t = n.err_msg),\n          delete n.err_msg,\n          (t = (function (e, n) {\n            var i = c[e];\n            i && (e = i);\n            i = \"ok\";\n            {\n              var t;\n              n &&\n                ((t = n.indexOf(\":\")),\n                (\"access denied\" !=\n                  (i = (i = (i =\n                    -1 !=\n                    (i =\n                      -1 !=\n                      (i =\n                        \"failed\" ==\n                        (i = \"confirm\" == (i = n.substring(t + 1)) ? \"ok\" : i)\n                          ? \"fail\"\n                          : i).indexOf(\"failed_\")\n                        ? i.substring(7)\n                        : i).indexOf(\"fail_\")\n                      ? i.substring(5)\n                      : i).replace(/_/g, \" \")).toLowerCase()) &&\n                  \"no permission to execute\" != i) ||\n                  (i = \"permission denied\"),\n                \"\" ==\n                  (i =\n                    \"config\" == e && \"function not exist\" == i ? \"ok\" : i)) &&\n                (i = \"fail\");\n            }\n            return (n = e + \":\" + i);\n          })(e, t)),\n          (n.errMsg = t)),\n        (i = i || {})._complete && (i._complete(n), delete i._complete),\n        (t = n.errMsg || \"\"),\n        h.debug && !i.isInnerInvoke && alert(JSON.stringify(n)),\n        t.indexOf(\":\"));\n    switch (t.substring(e + 1)) {\n      case \"ok\":\n        i.success && i.success(n);\n        break;\n      case \"cancel\":\n        i.cancel && i.cancel(n);\n        break;\n      default:\n        i.fail && i.fail(n);\n    }\n    i.complete && i.complete(n);\n  }\n  function A(e) {\n    if (e) {\n      for (var n = 0, i = e.length; n < i; ++n) {\n        var t = e[n],\n          t = a[t];\n        t && (e[n] = t);\n      }\n      return e;\n    }\n  }\n  function C(e, n) {\n    var i;\n    !h.debug ||\n      (n && n.isInnerInvoke) ||\n      ((i = c[e]) && (e = i),\n      n && n._complete && delete n._complete,\n      console.log('\"' + e + '\",', n || \"\"));\n  }\n  function B(n) {\n    var i;\n    o ||\n      s ||\n      h.debug ||\n      p < \"6.0.2\" ||\n      g.systemType < 0 ||\n      ((i = new Image()),\n      (g.appId = h.appId),\n      (g.initTime = m.initEndTime - m.initStartTime),\n      (g.preVerifyTime = m.preVerifyEndTime - m.preVerifyStartTime),\n      _.getNetworkType({\n        isInnerInvoke: !0,\n        success: function (e) {\n          g.networkType = e.networkType;\n          e =\n            \"https://open.weixin.qq.com/sdk/report?v=\" +\n            g.version +\n            \"&o=\" +\n            g.isPreVerifyOk +\n            \"&s=\" +\n            g.systemType +\n            \"&c=\" +\n            g.clientVersion +\n            \"&a=\" +\n            g.appId +\n            \"&n=\" +\n            g.networkType +\n            \"&i=\" +\n            g.initTime +\n            \"&p=\" +\n            g.preVerifyTime +\n            \"&u=\" +\n            g.url +\n            \"&jsapi_name=\" +\n            (n ? n.jsApiName : \"\");\n          i.src = e;\n        },\n      }));\n  }\n  function L() {\n    return new Date().getTime();\n  }\n  function O(e) {\n    d &&\n      (r.WeixinJSBridge\n        ? e()\n        : n.addEventListener &&\n          n.addEventListener(\"WeixinJSBridgeReady\", e, !1));\n  }\n});\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAE,SAAU,GAAG,GAAG;AAChB,aAAO,UAAU,EAAE,CAAC;AAAA,IACtB,EAAG,OAAO,WAAW,YAAY,QAAQ,SAAU,GAAG,GAAG;AACvD,UAAI,CAAC,GAAG;AACN,gBAAQ,KAAK,wCAAwC;AACrD;AAAA,MACF;AACA,UAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACnE,UAAI,CAAC,EAAE;AACL,eACG,IAAI;AAAA,UACH,QAAQ;AAAA,UACR,qBAAqB;AAAA,UACrB,uBAAuB;AAAA,UACvB,eAAe;AAAA,UACf,kBAAkB;AAAA,UAClB,kBAAkB;AAAA,UAClB,cAAc;AAAA,UACd,aAAa;AAAA,UACb,yBAAyB;AAAA,UACzB,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,UACb,yBAAyB;AAAA,UACzB,oBAAoB;AAAA,UACpB,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,UACjB,qBAAqB;AAAA,UACrB,aAAa;AAAA,QACf,GACC,IAAK,WAAY;AAChB,cAAIA,IACFC,KAAI,CAAC;AACP,eAAKD,MAAK;AAAG,YAAAC,GAAE,EAAED,EAAC,CAAC,IAAIA;AACvB,iBAAOC;AAAA,QACT,EAAG,GACF,IAAI,EAAE,UACN,IAAI,EAAE,OACN,IAAI,UAAU,UAAU,YAAY,GACpC,IAAI,UAAU,SAAS,YAAY,GACnC,IAAI,EAAE,CAAC,EAAE,MAAM,KAAK,KAAK,CAAC,EAAE,MAAM,KAAK,IACvC,IAAI,MAAM,EAAE,QAAQ,YAAY,GAChC,IAAI,MAAM,EAAE,QAAQ,gBAAgB,GACpC,IAAI,MAAM,EAAE,QAAQ,SAAS,GAC7B,IAAI,MAAM,EAAE,QAAQ,QAAQ,KAAK,MAAM,EAAE,QAAQ,MAAM,GACvD,KAAK,IACJ,EAAE,MAAM,iCAAiC,KACzC,EAAE,MAAM,4BAA4B,KAClC,EAAE,CAAC,IACH,IACH,IAAI;AAAA,UACH,eAAe,EAAE;AAAA,UACjB,aAAa;AAAA,UACb,oBAAoB;AAAA,UACpB,kBAAkB;AAAA,QACpB,GACC,IAAI;AAAA,UACH,SAAS;AAAA,UACT,OAAO;AAAA,UACP,UAAU;AAAA,UACV,eAAe;AAAA,UACf,aAAa;AAAA,UACb,eAAe;AAAA,UACf,YAAY,IAAI,IAAI,IAAI,IAAI;AAAA,UAC5B,eAAe;AAAA,UACf,KAAK,mBAAmB,SAAS,IAAI;AAAA,QACvC,GACC,IAAI,CAAC,GACL,IAAI,EAAE,YAAY,CAAC,EAAE,GACrB,IAAI,EAAE,OAAO,GAAG,MAAM,CAAC,EAAE,GAC1B,EAAE,WAAY;AACZ,YAAE,cAAc,EAAE;AAAA,QACpB,CAAC,GACA,IAAI,OACJ,IAAI,CAAC,GACL,IAAI;AAAA,UACH,QAAQ,SAAUD,IAAG;AACnB,cAAE,UAAW,IAAIA,EAAE;AACnB,gBAAIE,KAAI,UAAO,EAAE;AACjB,cAAE,WAAY;AACZ,kBAAIA;AACF;AAAA,kBACE,EAAE;AAAA,kBACF;AAAA,oBACE,iBAAiB,EAAE,EAAE,SAAS;AAAA,oBAC9B,mBAAmB,EAAE,EAAE,WAAW;AAAA,kBACpC;AAAA,mBACE,EAAE,YAAY,SAAUF,IAAG;AAC3B,oBAAC,EAAE,mBAAmB,EAAE,GAAK,EAAE,QAAQ,GAAK,EAAE,OAAOA;AAAA,kBACvD,GACC,EAAE,UAAU,SAAUA,IAAG;AACxB,sBAAE,gBAAgB;AAAA,kBACpB,GACC,EAAE,OAAO,SAAUA,IAAG;AACrB,sBAAE,QAAQ,EAAE,MAAMA,EAAC,IAAK,EAAE,QAAQ;AAAA,kBACpC,IACCG,KAAI,EAAE,YAAY,KAAK,WAAY;AAClC,sBAAE;AAAA,kBACJ,CAAC,GACA,EAAE,WAAW,SAAUH,IAAG;AACzB,6BAASC,KAAI,GAAGG,KAAID,GAAE,QAAQF,KAAIG,IAAG,EAAEH;AAAG,sBAAAE,GAAEF,EAAC,EAAE;AAC/C,sBAAE,aAAa,CAAC;AAAA,kBAClB,GACA;AAAA,gBACF,GACG,EAAE,qBAAqB,EAAE;AAAA,mBACzB;AACH,kBAAE,QAAQ;AACV,yBAASD,KAAI,EAAE,YAAYC,KAAI,GAAGG,KAAIJ,GAAE,QAAQC,KAAIG,IAAG,EAAEH;AACvD,kBAAAD,GAAEC,EAAC,EAAE;AACP,kBAAE,aAAa,CAAC;AAAA,cAClB;AACA,kBAAIE;AAAA,YACN,CAAC,GACC,EAAE,WACE,EAAE,SAAS,SAAUH,IAAGC,IAAGG,IAAG;AAC9B,gBAAE,kBAAkB,eAAe,OAAOJ,IAAG,EAAEC,EAAC,GAAGG,EAAC;AAAA,YACtD,GACC,EAAE,KAAK,SAAUJ,IAAGC,IAAG;AACtB,gBAAE,kBAAkB,eAAe,GAAGD,IAAGC,EAAC;AAAA,YAC5C;AAAA,UACN;AAAA,UACA,OAAO,SAAUD,IAAG;AAClB,aAAC,KAAK,EAAE,UAAU,EAAE,WAAW,KAAKA,EAAC,GAAG,CAAC,KAAK,EAAE,WAAWA,GAAE;AAAA,UAC/D;AAAA,UACA,OAAO,SAAUA,IAAG;AAClB,gBAAI,YAAY,MAAM,EAAE,QAAQA,GAAE,EAAE,IAAI,IAAK,EAAE,QAAQA;AAAA,UACzD;AAAA,UACA,YAAY,SAAUA,IAAG;AACvB;AAAA,cACE;AAAA,cACA,EAAE,WAAW,EAAEA,GAAE,SAAS,EAAE;AAAA,eAC1BA,GAAE,YAAY,SAAUA,IAAG;AAC3B,sBAAMI,KAAIJ,GAAE,iBAAiBA,GAAE,cAAc,KAAK,MAAMI,EAAC;AACzD,oBAAIH,IACFG,KAAIJ,IACJG,KAAIC,GAAE;AACR,qBAAKH,MAAKE,IAAG;AACX,sBAAID,KAAI,EAAED,EAAC;AACX,kBAAAC,OAAOC,GAAED,EAAC,IAAIC,GAAEF,EAAC,GAAI,OAAOE,GAAEF,EAAC;AAAA,gBACjC;AAAA,cACF,GACAD;AAAA,YACF;AAAA,UACF;AAAA,UACA,qBAAqB,SAAUA,IAAG;AAChC;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,WAAY;AACpB;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,SAAS;AAAA,sBACjB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ,SAAS;AAAA,sBACzB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,UAAUA,GAAE,WAAW;AAAA,oBACzB;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,uBAAuB,SAAUC,IAAG;AAClC;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,SAAUD,IAAG;AACrB,iCAAeA,GAAE,QACb,EAAE,kBAAkB;AAAA,oBAClB,OAAOC,GAAE,SAAS;AAAA,oBAClB,MAAMA,GAAE,QAAQ;AAAA,oBAChB,MAAMA,GAAE,QAAQ,SAAS;AAAA,oBACzB,SAASA,GAAE,UAAU;AAAA,oBACrB,MAAMA,GAAE,QAAQ;AAAA,oBAChB,UAAUA,GAAE,WAAW;AAAA,kBACzB,CAAC,IACD;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,MAAMA,GAAE,QAAQ,SAAS;AAAA,sBACzB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,UAAUA,GAAE,WAAW;AAAA,oBACzB;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACN;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,eAAe,SAAUD,IAAG;AAC1B;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,WAAY;AACpB;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ,SAAS;AAAA,oBAC3B;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,kBAAkB,SAAUA,IAAG;AAC7B;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,WAAY;AACpB;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ,SAAS;AAAA,oBAC3B;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,kBAAkB,SAAUA,IAAG;AAC7B;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,WAAY;AACpB;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ,SAAS;AAAA,oBAC3B;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,yBAAyB,SAAUA,IAAG;AACpC;AAAA,cACE;AAAA,cACA,EAAE,OAAOA,GAAE,OAAO,MAAMA,GAAE,MAAM,QAAQA,GAAE,OAAO;AAAA,cACjDA;AAAA,YACF;AAAA,UACF;AAAA,UACA,2BAA2B,SAAUA,IAAG;AACtC;AAAA,cACE;AAAA,cACA,EAAE,OAAOA,GAAE,OAAO,MAAMA,GAAE,MAAM,MAAMA,GAAE,MAAM,QAAQA,GAAE,OAAO;AAAA,cAC/DA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAAUA,IAAG;AACxB,cAAE,eAAe,CAAC,GAAGA,EAAC;AAAA,UACxB;AAAA,UACA,YAAY,SAAUA,IAAG;AACvB,cAAE,cAAc,CAAC,GAAGA,EAAC;AAAA,UACvB;AAAA,UACA,kBAAkB,SAAUA,IAAG;AAC7B,cAAE,oBAAoBA,EAAC;AAAA,UACzB;AAAA,UACA,WAAW,SAAUA,IAAG;AACtB,cAAE,aAAa,EAAE,SAASA,GAAE,QAAQ,GAAGA,EAAC;AAAA,UAC1C;AAAA,UACA,YAAY,SAAUA,IAAG;AACvB,cAAE,cAAc,EAAE,SAASA,GAAE,QAAQ,GAAGA,EAAC;AAAA,UAC3C;AAAA,UACA,WAAW,SAAUA,IAAG;AACtB,cAAE,aAAa,EAAE,SAASA,GAAE,QAAQ,GAAGA,EAAC;AAAA,UAC1C;AAAA,UACA,gBAAgB,SAAUA,IAAG;AAC3B,cAAE,kBAAkBA,EAAC;AAAA,UACvB;AAAA,UACA,aAAa,SAAUA,IAAG;AACxB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,SAASA,GAAE;AAAA,gBACX,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,eAAe,SAAUA,IAAG;AAC1B;AAAA,cACE;AAAA,cACA;AAAA,gBACE,UAAUA,GAAE;AAAA,gBACZ,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,gBAAgB,SAAUA,IAAG;AAC3B;AAAA,cACE;AAAA,cACA;AAAA,gBACE,SAASA,GAAE;AAAA,gBACX,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAAUA,IAAG;AACxB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,OAAOA,GAAE,SAAS;AAAA,gBAClB,UAAUA,GAAE,YAAY,CAAC,YAAY,YAAY;AAAA,gBACjD,YAAYA,GAAE,cAAc,CAAC,SAAS,QAAQ;AAAA,cAChD;AAAA,eACEA,GAAE,YAAY,SAAUA,IAAG;AAC3B,oBAAI,GAAG;AACL,sBAAIC,KAAID,GAAE;AACV,sBAAI;AACF,oBAAAC,OAAMD,GAAE,WAAW,KAAK,MAAMC,EAAC;AAAA,kBACjC,SAASD,IAAG;AAAA,kBAAC;AAAA,gBACf;AAAA,cACF,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAAUA,IAAG;AACxB,YAACA,KAAIA,MAAK,CAAC,GACT;AAAA,cACE,EAAE;AAAA,cACF,EAAE,MAAMA,GAAE,QAAQ,QAAQ;AAAA,eACxBA,GAAE,YAAY,SAAUA,IAAG;AAC3B,uBAAOA,GAAE;AAAA,cACX,GACAA;AAAA,YACF;AAAA,UACJ;AAAA,UACA,cAAc,SAAUA,IAAG;AACzB,cAAE,EAAE,cAAc,EAAE,SAASA,GAAE,SAAS,MAAMA,GAAE,KAAK,GAAGA,EAAC;AAAA,UAC3D;AAAA,UACA,aAAa,SAAUA,IAAG;AACxB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,SAASA,GAAE;AAAA,gBACX,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,eAAe,SAAUA,IAAG;AAC1B;AAAA,cACE;AAAA,cACA;AAAA,gBACE,UAAUA,GAAE;AAAA,gBACZ,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,iBAAiB,SAAUA,IAAG;AAC5B,sBAAO,KACD,IAAI,MACN;AAAA,cACE;AAAA,cACA,EAAE,SAASA,GAAE,QAAQ;AAAA,eACnBA,GAAE,YAAY,SAAUA,IAAG;AAC3B,oBAAIC;AACJ,gBAAC,IAAI,OACH,IAAI,EAAE,WAAYA,KAAI,EAAE,MAAM,GAAI,GAAG,gBAAgBA,EAAC;AAAA,cAC1D,GACAD;AAAA,YACF,KACA,EAAE,KAAKA,EAAC;AAAA,UACd;AAAA,UACA,gBAAgB,SAAUA,IAAG;AAC3B;AAAA,cACE;AAAA,cACA,CAAC;AAAA,eACCA,GAAE,YAAY,SAAUA,IAAG;AAC3B,oBAAIC,KAAID,IACNA,KAAIC,GAAE,QACNG,MAAMH,GAAE,SAAS,qBAAsBA,GAAE;AAC3C,oBAAK,OAAOA,GAAE,SAASG;AAAI,kBAAAH,GAAE,cAAcG;AAAA,qBACtC;AACH,sBAAIA,KAAIJ,GAAE,QAAQ,GAAG,GACnBG,KAAIH,GAAE,UAAUI,KAAI,CAAC;AACvB,0BAAQD,IAAG;AAAA,oBACT,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AACH,sBAAAF,GAAE,cAAcE;AAChB;AAAA,oBACF;AACE,sBAAAF,GAAE,SAAS;AAAA,kBACf;AAAA,gBACF;AAAA,cACF,GACAD;AAAA,YACF;AAAA,UACF;AAAA,UACA,cAAc,SAAUA,IAAG;AACzB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,UAAUA,GAAE;AAAA,gBACZ,WAAWA,GAAE;AAAA,gBACb,MAAMA,GAAE,QAAQ;AAAA,gBAChB,SAASA,GAAE,WAAW;AAAA,gBACtB,OAAOA,GAAE,SAAS;AAAA,gBAClB,SAASA,GAAE,WAAW;AAAA,cACxB;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,gBAAgB,SAAUA,IAAG;AAC3B,cAAE,kBAAkB,CAAC,GAAGA,EAAC;AAAA,UAC3B;AAAA,UACA,gBAAgB,SAAUA,IAAG;AAC3B,cAAE,kBAAkB,CAAC,GAAGA,EAAC;AAAA,UAC3B;AAAA,UACA,aAAa,SAAUA,IAAG;AACxB,cAAE,eAAe,CAAC,GAAIA,KAAIA,MAAK,CAAC,CAAE;AAAA,UACpC;AAAA,UACA,eAAe,SAAUA,IAAG;AAC1B,cAAE,iBAAiB,EAAE,UAAUA,GAAE,SAAS,GAAGA,EAAC;AAAA,UAChD;AAAA,UACA,eAAe,SAAUA,IAAG;AAC1B,cAAE,iBAAiB,EAAE,UAAUA,GAAE,SAAS,GAAGA,EAAC;AAAA,UAChD;AAAA,UACA,wBAAwB,SAAUA,IAAG;AACnC,cAAE,0BAA0B,CAAC,GAAGA,EAAC;AAAA,UACnC;AAAA,UACA,wBAAwB,SAAUA,IAAG;AACnC,cAAE,0BAA0B,CAAC,GAAGA,EAAC;AAAA,UACnC;AAAA,UACA,YAAY,SAAUA,IAAG;AACvB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,aAAaA,KAAIA,MAAK,CAAC,GAAG,cAAc;AAAA,gBACxC,UAAUA,GAAE,YAAY,CAAC,UAAU,SAAS;AAAA,cAC9C;AAAA,eACEA,GAAE,YAAY,SAAUA,IAAG;AAC3B,oBAAIC;AACJ,sBACGA,KAAID,GAAE,eACLC,KAAI,KAAK,MAAMA,EAAC,GACjBD,GAAE,YAAYC,MAAKA,GAAE,aAAaA,GAAE,UAAU;AAAA,cACnD,GACAD;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAAUA,IAAG;AACxB;AAAA,cACE,EAAE;AAAA,cACF,CAAC;AAAA,eACCA,GAAE,YAAY,SAAUA,IAAG;AAC3B,iBAAEA,KAAIA,IAAG,aAAaA,GAAE,mBACtB,OAAOA,GAAE,mBACRA,GAAE,eAAeA,GAAE,uBACpB,OAAOA,GAAE,uBACRA,GAAE,WAAWA,GAAE,4BAChB,OAAOA,GAAE,4BACRA,GAAE,cAAcA,GAAE,+BACnB,OAAOA,GAAE,+BACRA,GAAE,aAAaA,GAAE,mBAClB,OAAOA,GAAE;AAAA,cACb,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,yBAAyB,SAAUA,IAAG;AACpC;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,KAAKA,GAAE;AAAA,gBACP,WAAWA,GAAE,YAAY;AAAA,gBACzB,UAAUA,GAAE;AAAA,cACd;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS,SAAUA,IAAG;AACpB,qBAASC,KAAID,GAAE,UAAUI,KAAI,CAAC,GAAGD,KAAI,GAAGD,KAAID,GAAE,QAAQE,KAAID,IAAG,EAAEC,IAAG;AAChE,kBAAIE,KAAIJ,GAAEE,EAAC,GACTE,KAAI,EAAE,SAASA,GAAE,QAAQ,UAAUA,GAAE,QAAQ;AAC/C,cAAAD,GAAE,KAAKC,EAAC;AAAA,YACV;AACA;AAAA,cACE,EAAE;AAAA,cACF,EAAE,WAAWD,GAAE;AAAA,eACbJ,GAAE,YAAY,SAAUA,IAAG;AAC3B,oBAAKC,KAAID,GAAE,WAAY;AACrB,2BAASC,IAAGG,KAAI,GAAGD,MAAKF,KAAI,KAAK,MAAMA,EAAC,GAAG,QAAQG,KAAID,IAAG,EAAEC,IAAG;AAC7D,wBAAIF,KAAID,GAAEG,EAAC;AACX,oBAACF,GAAE,SAASA,GAAE,SACXA,GAAE,UAAUA,GAAE,UACdA,GAAE,YAAY,CAAC,CAACA,GAAE,SACnB,OAAOA,GAAE,SACT,OAAOA,GAAE,UACT,OAAOA,GAAE;AAAA,kBACb;AACA,kBAACF,GAAE,WAAWC,IAAI,OAAOD,GAAE;AAAA,gBAC7B;AAAA,cACF,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,YAAY,SAAUA,IAAG;AACvB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,QAAQ,EAAE;AAAA,gBACV,aAAaA,GAAE,UAAU;AAAA,gBACzB,WAAWA,GAAE,YAAY;AAAA,gBACzB,SAASA,GAAE,UAAU;AAAA,gBACrB,WAAWA,GAAE,YAAY;AAAA,gBACzB,WAAWA,GAAE;AAAA,gBACb,YAAYA,GAAE,YAAY;AAAA,gBAC1B,WAAWA,GAAE;AAAA,cACf;AAAA,eACEA,GAAE,YAAY,SAAUA,IAAG;AAC3B,gBAACA,GAAE,WAAWA,GAAE,kBAAmB,OAAOA,GAAE;AAAA,cAC9C,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,UAAU,SAAUA,IAAG;AACrB,qBAASC,KAAID,GAAE,UAAUI,KAAI,CAAC,GAAGD,KAAI,GAAGD,KAAID,GAAE,QAAQE,KAAID,IAAG,EAAEC,IAAG;AAChE,kBAAIE,KAAIJ,GAAEE,EAAC,GACTE,KAAI,EAAE,SAASA,GAAE,QAAQ,MAAMA,GAAE,KAAK;AACxC,cAAAD,GAAE,KAAKC,EAAC;AAAA,YACV;AACA,cAAE,EAAE,UAAU,EAAE,WAAWD,GAAE,GAAGJ,EAAC;AAAA,UACnC;AAAA,UACA,qBAAqB,SAAUA,IAAG;AAChC;AAAA,cACE,EAAE;AAAA,cACF,EAAE,gBAAgBA,GAAE,QAAQ,cAAcA,GAAE,KAAK;AAAA,cACjDA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAAUA,IAAG;AACxB,cAAE,EAAE,aAAa,EAAEA,EAAC,GAAGA,EAAC,GAAG,EAAE,EAAE,WAAW,cAAc,CAAC;AAAA,UAC3D;AAAA,UACA,yBAAyB,SAAUA,IAAG;AACpC,cAAE,EAAE,yBAAyB,EAAEA,EAAC,GAAGA,EAAC;AAAA,UACtC;AAAA,UACA,oBAAoB,SAAUA,IAAG;AAC/B,cAAE,EAAE,oBAAoB,EAAE,QAAQA,GAAE,OAAO,GAAGA,EAAC;AAAA,UACjD;AAAA,UACA,mBAAmB,SAAUA,IAAG;AAC9B,cAAE,EAAE,mBAAmB,CAAC,GAAGA,EAAC;AAAA,UAC9B;AAAA,UACA,iBAAiB,SAAUA,IAAG;AAC5B,cAAE,EAAE,iBAAiBA,EAAC;AAAA,UACxB;AAAA,UACA,oBAAoB,SAAUA,IAAG;AAC/B;AAAA,cACE;AAAA,cACA,EAAE,YAAYA,GAAE,SAAS,UAAUA,GAAE,UAAU;AAAA,cAC/CA;AAAA,YACF;AAAA,UACF;AAAA,UACA,mBAAmB,SAAUA,IAAG;AAC9B;AAAA,cACE;AAAA,cACA;AAAA,gBACE,aAAaA,GAAE;AAAA,gBACf,MAAO,SAAUA,IAAG;AAClB,sBAAIC;AACJ,sBAAI,YAAY,OAAOD,MAAK,IAAIA,GAAE;AAChC,2BACGC,KAAID,GAAE,MAAM,GAAG,EAAE,CAAC,GAClBC,MAAK,SACN,YAAYD,KAAIA,GAAE,MAAM,GAAG,EAAE,CAAC,KAAKC,KAAI,MAAMD,KAAIC;AAAA,gBAEvD,EAAGD,GAAE,IAAI;AAAA,gBACT,YAAYA,GAAE;AAAA,cAChB;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,kBAAkB,SAAUA,IAAG;AAC7B;AAAA,cACE;AAAA,cACA;AAAA,gBACE,cAAcA,GAAE;AAAA,gBAChB,aAAaA,GAAE,eAAe;AAAA,gBAC9B,YAAYA,GAAE;AAAA,cAChB;AAAA,eACEA,GAAE,YAAY,SAAUC,IAAG;AAC3B,oBAAI,GAAG;AACL,sBAAID,KAAIC,GAAE;AACV,sBAAID;AACF,wBAAI;AACF,sBAAAC,GAAE,YAAY,KAAK,MAAMD,EAAC;AAAA,oBAC5B,SAASA,IAAG;AACV,sBAAAC,GAAE,YAAY,CAAC;AAAA,oBACjB;AAAA,gBACJ;AAAA,cACF,GACAD;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa;AAAA,YACX,cAAc,SAAUA,IAAG;AACzB,cAACA,KAAIA,MAAK,CAAC,GACT,EAAE,WAAY;AACZ;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,gBAAgB,KAAK,EAAE,OAAOA,GAAE,SAAS,EAAE,EAAE;AAAA,kBACrDA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACL;AAAA,YACA,YAAY,SAAUA,IAAG;AACvB,gBAAE,WAAY;AACZ;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,cAAc,KAAK,EAAE,KAAKA,GAAE,IAAI,EAAE;AAAA,kBAC1CA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,YAAY,SAAUA,IAAG;AACvB,gBAAE,WAAY;AACZ;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,cAAc,KAAK,EAAE,KAAKA,GAAE,IAAI,EAAE;AAAA,kBAC1CA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,WAAW,SAAUA,IAAG;AACtB,gBAAE,WAAY;AACZ;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,aAAa,KAAK,EAAE,KAAKA,GAAE,IAAI,EAAE;AAAA,kBACzCA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,UAAU,SAAUA,IAAG;AACrB,gBAAE,WAAY;AACZ;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,YAAY,KAAK,EAAE,KAAKA,GAAE,IAAI,EAAE;AAAA,kBACxCA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,aAAa,SAAUA,IAAG;AACxB,gBAAE,WAAY;AACZ;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,eAAe,KAAKA,GAAE,QAAQ,CAAC,EAAE;AAAA,kBACzCA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,QAAQ,SAAUA,IAAG;AACnB,gBAAE,WAAY;AACZ,gBAAAA,GAAE,EAAE,aAAa,kBAAkB,EAAE,mBAAmB,CAAC;AAAA,cAC3D,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,GACC,IAAI,GACJ,IAAI,CAAC,GACN,EAAE;AAAA,UACA;AAAA,UACA,SAAUA,IAAG;AACX,gBAAIC,IAAGG,IAAGD;AACV,kBACIA,MAAKF,KAAID,GAAE,QAAQ,SACpBI,KAAIH,GAAE,KACP,SAASE,MAAK,WAAWA,MAAK,WAAWA,MAAK,YAAYA,OACzD,MAAMC,GAAE,QAAQ,oBAAoB,MAClCJ,GAAE,eAAe,GAClBA,GAAE,gBAAgB,IACjBG,KAAIF,GAAE,OAAO,OAAQE,KAAI,KAAOF,GAAE,OAAO,IAAIE,KAC9C,EAAEA,EAAC,MACC,EAAEA,EAAC,IAAI,MACT,GAAG,MAAM,WAAY;AACnB,iBAAG,gBAAgB;AAAA,gBACjB,SAASC;AAAA,gBACT,SAAS,SAAUJ,IAAG;AACpB,kBAAAC,GAAE,MAAMD,GAAE;AAAA,gBACZ;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AAAA,UACT;AAAA,UACA;AAAA,QACF,GACA,EAAE;AAAA,UACA;AAAA,UACA,SAAUA,IAAG;AACX,gBAAIC;AACJ,kBACIA,MAAKD,KAAIA,GAAE,QAAQ,SACrBA,GAAE,KACF,SAASC,MAAK,WAAWA,MAAK,WAAWA,MAAK,YAAYA,QACxDA,KAAID,GAAE,OAAO,OAAO,EAAEC,EAAC,IAAI;AAAA,UACjC;AAAA,UACA;AAAA,QACF,GACA,MAAM,EAAE,KAAK,EAAE,UAAU,IACzB;AAAA;AAEC,eAAO,EAAE;AACd,eAAS,EAAEA,IAAGD,IAAGI,IAAG;AAClB,UAAE,iBACE,eAAe,OAAOH,IAAG,EAAED,EAAC,GAAG,SAAUA,IAAG;AAC1C,YAAEC,IAAGD,IAAGI,EAAC;AAAA,QACX,CAAC,IACD,EAAEH,IAAGG,EAAC;AAAA,MACZ;AACA,eAAS,EAAEH,IAAGG,IAAGD,IAAG;AAClB,UAAE,iBACE,eAAe,GAAGF,IAAG,SAAUD,IAAG;AAChC,UAAAG,MAAKA,GAAE,WAAWA,GAAE,QAAQH,EAAC,GAAG,EAAEC,IAAGD,IAAGI,EAAC;AAAA,QAC3C,CAAC,IACD,EAAEH,IAAGE,MAAKC,EAAC;AAAA,MACjB;AACA,eAAS,EAAEJ,IAAG;AACZ,gBACIA,KAAIA,MAAK,CAAC,GAAG,QAAQ,EAAE,OACxBA,GAAE,cAAc,EAAE,OAClBA,GAAE,iBAAiB,QACnBA,GAAE,kBAAkB,EAAE,YAAY,IAClCA,GAAE,iBAAiB,EAAE,UACrBA,GAAE,kBAAkB,EAAE,WACvBA;AAAA,MAEJ;AACA,eAAS,EAAEA,IAAG;AACZ,eAAO;AAAA,UACL,WAAWA,GAAE,YAAY;AAAA,UACzB,UAAUA,GAAE;AAAA,UACZ,SAASA,GAAE;AAAA,UACX,SAASA,GAAE;AAAA,UACX,UAAUA,GAAE,YAAY;AAAA,QAC1B;AAAA,MACF;AACA,eAAS,EAAEA,IAAGC,IAAGG,IAAG;AAClB,QAAC,wBAAwBJ,MAAK,uBAAuBA,OAClDC,GAAE,UAAUA,GAAE,WACf,OAAOA,GAAE,UACT,OAAOA,GAAE,UACT,OAAOA,GAAE;AACX,YAAIE,KAAIF,GAAE,QACRD,MACGG,OACGA,KAAIF,GAAE,SACR,OAAOA,GAAE,SACRE,KAAK,SAAUH,IAAGC,IAAG;AACpB,cAAIG,KAAI,EAAEJ,EAAC;AACX,UAAAI,OAAMJ,KAAII;AACV,UAAAA,KAAI;AACJ;AACE,gBAAID;AACJ,YAAAF,OACIE,KAAIF,GAAE,QAAQ,GAAG,GAClB,oBACEG,MAAKA,MAAKA,KACT,OACCA,KACC,OACCA,KACC,aACCA,KAAI,cAAcA,KAAIH,GAAE,UAAUE,KAAI,CAAC,KAAK,OAAOC,MAChD,SACAA,IAAG,QAAQ,SAAS,IACtBA,GAAE,UAAU,CAAC,IACbA,IAAG,QAAQ,OAAO,IACpBA,GAAE,UAAU,CAAC,IACbA,IAAG,QAAQ,MAAM,GAAG,GAAG,YAAY,MACzC,8BAA8BA,OAC7BA,KAAI,sBACP,OACGA,KACC,YAAYJ,MAAK,wBAAwBI,KAAI,OAAOA,SACvDA,KAAI;AAAA,UACT;AACA,iBAAQH,KAAID,KAAI,MAAMI;AAAA,QACxB,EAAGJ,IAAGG,EAAC,GACNF,GAAE,SAASE,MACbC,KAAIA,MAAK,CAAC,GAAG,cAAcA,GAAE,UAAUH,EAAC,GAAG,OAAOG,GAAE,YACpDD,KAAIF,GAAE,UAAU,IACjB,EAAE,SAAS,CAACG,GAAE,iBAAiB,MAAM,KAAK,UAAUH,EAAC,CAAC,GACtDE,GAAE,QAAQ,GAAG;AACjB,gBAAQA,GAAE,UAAUH,KAAI,CAAC,GAAG;AAAA,UAC1B,KAAK;AACH,YAAAI,GAAE,WAAWA,GAAE,QAAQH,EAAC;AACxB;AAAA,UACF,KAAK;AACH,YAAAG,GAAE,UAAUA,GAAE,OAAOH,EAAC;AACtB;AAAA,UACF;AACE,YAAAG,GAAE,QAAQA,GAAE,KAAKH,EAAC;AAAA,QACtB;AACA,QAAAG,GAAE,YAAYA,GAAE,SAASH,EAAC;AAAA,MAC5B;AACA,eAAS,EAAED,IAAG;AACZ,YAAIA,IAAG;AACL,mBAASC,KAAI,GAAGG,KAAIJ,GAAE,QAAQC,KAAIG,IAAG,EAAEH,IAAG;AACxC,gBAAIE,KAAIH,GAAEC,EAAC,GACTE,KAAI,EAAEA,EAAC;AACT,YAAAA,OAAMH,GAAEC,EAAC,IAAIE;AAAA,UACf;AACA,iBAAOH;AAAA,QACT;AAAA,MACF;AACA,eAAS,EAAEA,IAAGC,IAAG;AACf,YAAIG;AACJ,SAAC,EAAE,SACAH,MAAKA,GAAE,mBACNG,KAAI,EAAEJ,EAAC,OAAOA,KAAII,KACpBH,MAAKA,GAAE,aAAa,OAAOA,GAAE,WAC7B,QAAQ,IAAI,MAAMD,KAAI,MAAMC,MAAK,EAAE;AAAA,MACvC;AACA,eAAS,EAAEA,IAAG;AACZ,YAAIG;AACJ,aACE,KACA,EAAE,SACF,IAAI,WACJ,EAAE,aAAa,MACbA,KAAI,IAAI,MAAM,GACf,EAAE,QAAQ,EAAE,OACZ,EAAE,WAAW,EAAE,cAAc,EAAE,eAC/B,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,oBAC1C,EAAE,eAAe;AAAA,UACf,eAAe;AAAA,UACf,SAAS,SAAUJ,IAAG;AACpB,cAAE,cAAcA,GAAE;AAClB,YAAAA,KACE,6CACA,EAAE,UACF,QACA,EAAE,gBACF,QACA,EAAE,aACF,QACA,EAAE,gBACF,QACA,EAAE,QACF,QACA,EAAE,cACF,QACA,EAAE,WACF,QACA,EAAE,gBACF,QACA,EAAE,MACF,kBACCC,KAAIA,GAAE,YAAY;AACrB,YAAAG,GAAE,MAAMJ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACL;AACA,eAAS,IAAI;AACX,gBAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,MAC5B;AACA,eAAS,EAAEA,IAAG;AACZ,cACG,EAAE,iBACCA,GAAE,IACF,EAAE,oBACF,EAAE,iBAAiB,uBAAuBA,IAAG,KAAE;AAAA,MACvD;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["e", "n", "o", "t", "i", "r"]}