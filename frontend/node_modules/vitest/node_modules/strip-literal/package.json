{"name": "strip-literal", "version": "1.3.0", "packageManager": "pnpm@8.6.11", "description": "Strip comments and string literals from JavaScript code", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/strip-literal#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/strip-literal.git"}, "bugs": {"url": "https://github.com/antfu/strip-literal/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "prepublishOnly": "nr build", "release": "bumpp --commit --push --tag && npm publish", "start": "esmo src/index.ts", "test": "vitest", "bench": "vitest bench", "typecheck": "tsc --noEmit"}, "dependencies": {"acorn": "^8.10.0"}, "devDependencies": {"@antfu/eslint-config": "^0.40.0", "@antfu/ni": "^0.21.5", "@types/node": "^20.4.5", "bumpp": "^9.1.1", "eslint": "^8.46.0", "esmo": "^0.17.0", "pnpm": "^8.6.11", "rimraf": "^5.0.1", "three": "^0.155.0", "typescript": "^5.1.6", "unbuild": "^1.2.1", "vite": "^4.4.8", "vitest": "^0.34.1", "vue": "^3.3.4"}}