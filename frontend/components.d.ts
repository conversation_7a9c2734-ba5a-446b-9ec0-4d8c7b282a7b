/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    PrizeModal: typeof import('./src/components/PrizeModal/index.vue')['default']
    PuzzleBoard: typeof import('./src/components/PuzzleBoard/index.vue')['default']
    QRScanner: typeof import('./src/components/QRScanner/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanDialog: typeof import('vant/es')['Dialog']
    VanLoading: typeof import('vant/es')['Loading']
    VanNotify: typeof import('vant/es')['Notify']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanToast: typeof import('vant/es')['Toast']
  }
}
